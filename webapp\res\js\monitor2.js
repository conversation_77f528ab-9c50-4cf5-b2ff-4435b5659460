$(function () {
    $('input').bind('input propertychange', function () {
        $('.commonTable tbody tr').hide()
            .filter(":contains('" + ($(this).val()) + "')").show();
    });

    $("#refreshBtn").click(function () {
        init();
    });
});
var xmlHttp = false;
function createXMLHttpRequest() {
	if (window.ActiveXObject) {
		xmlHttp = new ActiveXObject("Microsoft.XMLHTTP");
	}
	else if (window.XMLHttpRequest) {
		xmlHttp = new XMLHttpRequest();
	}
}
function init() {
	
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = initcallBack;
    var url="QueryCellInfoServlet";  
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);
}
function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data=JSON.parse(data);
			//outdevinfo(data[0]);
			//devchart(data[1]);
			initTable(data[0]);	
		}
	}
}

function initTable(myData) {
    
    var HTML = "<thead>\n" +
        "        <td title=\"序号\">序号</td>\n" +
        "        <td title=\"基站ID\">基站ID</td>\n" +
         "        <td title=\"场景\">场景</td>\n" +
        "        <td title=\"PCI\">PCI</td>\n" +
        "        <td title=\"频点\">频点</td>\n" +
        "        <td title=\"服务状态\">小区状态</td>\n" +
        "        <td title=\"更新时间\">更新时间</td>\n" +
        "        </thead>\n" +
        "        <tbody>\n";
        
    $(myData).each(function (index, ele) {
        	HTML += "<tr>\n" +
            "            <td>" + (index + 1) + "</td>\n" +
            "            <td>" + ele['gnbid'] + "</td>\n"+
             "            <td>" + ele['scene'] + "</td>\n"+
            "            <td>" + ele['pci'] + "</td>\n"+
            "            <td>" + ele['ssbFrequency'] + "</td>\n" ;
            if(ele['serviceStatus']=="在服")
            {	HTML+="  <td>" + ele['serviceStatus'] + "</td>\n";}
            else
            { 	HTML+=  "<td><span style='color: #FF0000;'>" + ele['serviceStatus'] + "</span></td>\n";}
            HTML+=	 "<td>" + ele['updatetime'] + "</td>\n";
    });
    HTML += "</tbody>";
    $('.commonTable').html(HTML);

    $('.commonTable tbody tr').hide()
        .filter(":contains('" + ($("#searchText").val()) + "')").show();

}

