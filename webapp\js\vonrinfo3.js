/**
 * vonrinfo3.js - 语音Log上传功能
 * 基于vonrinfo2.js和vonrinfo4.js实现
 */
var myjson;
var subTableId; // 定义全局变量
var logininfo = islogininfo(); // 获取登录信息

layui.use(function () {
    var laypage = layui.laypage;
    var layer = layui.layer;
    var table = layui.table;
    var upload = layui.upload;
    var element = layui.element;

    // 渲染主表格
    function renderMainTable() {
        table.render({
            type: 'post',
            elem: '#table0',
            url: 'QueryVonrTableServlet2', // 此处为静态模拟数据，实际使用时需换成真实接口
            even: false,
            toolbar: '#page0',
            id: 'testReload0',
            request: {
                pageName: 'pageIndex',  // 页码的参数名称，默认：page
                limitName: 'pageSize'   // 每页数据量的参数名，默认：limit
            },
            response: {
                statusName: 'code',     // 规定数据状态的字段名称，默认：code
                statusCode: 0,          // 规定成功的状态码，默认：0
                countName: 'count', // 规定数据总数的字段名称，默认：count
                msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
            },
            cols: [[
                { field: 'dataid', title: '任务ID', width: 80 },
                { field: 'rowId', title: '子任务ID', width: 80 },
                { field: 'status', title: '状态', width: 120, templet: function (d) {
                    if (d.status === "success") {
                        return '<span style="color: green;">' + d.status + '</span>';
                    } else if (d.status === "fail") {
                        return '<span style="color: red;">' + d.status + '</span>';
                    }
                } },
                { field: 'start', title: '开始时间', width: 200 },
                { field: 'end', title: '结束时间', width: 200 },
                { field: 'filename', title: '文件名', width: 300 },
                { field: 'result', title: '结果', width: 150 },
                { field: 'lossInfo', title: '详情', width: 250 },
                { field: 'operate', title: '操作', width: 150, toolbar: '#barDemo' },
            ]],
            done: function (res, curr, count) {
                bindMainTableToolEvent();
                	     var that = $("#table0").siblings();

//                	console.log(res);
                     res.data.forEach(function (item, index) {
			         if (item.rowId=="total") {
			            var tr = that.find(".layui-table-box tbody tr[data-index='" + index + "']");
			            tr.css("background-color", "#F1FAFA");
			         }
			    });
            },
            page: {
                layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                curr: 1,      // 设置默认起始页1
                groups: 5,   //只显示10个连续页码,就是说显示10个可见页其他的省略
                first: false, // 不显示首页
                last: false   // 不显示尾页
            },
            text: {
                none: '暂无相关数据'
            },
            limit: 20,
            limits: [5, 10, 15, 20, 100, 1000]
        });
    }

    // 渲染上传结果记录表格
    function renderLog2Table() {
        table.render({
            type: 'post',
            elem: '#table2',
            url: 'QueryVonrLog2TableServlet', // 查询aivonrlog2表的接口
            even: false,
            id: 'testReload2',
            request: {
                pageName: 'pageIndex',  // 页码的参数名称，默认：page
                limitName: 'pageSize'   // 每页数据量的参数名，默认：limit
            },
            response: {
                statusName: 'code',     // 规定数据状态的字段名称，默认：code
                statusCode: 0,          // 规定成功的状态码，默认：0
                countName: 'count', // 规定数据总数的字段名称，默认：count
                msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
            },
            cols: [[
                { title: 'ID', width: 80,type:"numbers" },
//                { field: 'dataid', title: '数据ID', width: 100 },
                { field: 'userid', title: '用户ID', width: 120 },
                { field: 'uploadtime', title: '上传时间', width: 180 },
                { field: 'status', title: '状态', width: 180 },
                { field: 'taskid', title: '任务ID', width: 200 },
                { field: 'filename', title: '文件', width: 180 },
                { field: 'query', title: '查询操作', width: 120, toolbar: '#logQueryBar' }
            ]],
            page: {
                layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                curr: 1,      // 设置默认起始页1
                groups: 5,   //只显示5个连续页码
                first: false, // 不显示首页
                last: false   // 不显示尾页
            },
            text: {
                none: '暂无相关数据'
            },
            limit: 20,
            limits: [5, 10, 15, 20, 100, 1000]
        });
    }

    // 绑定主表格工具条事件
    function bindMainTableToolEvent() {
        table.on('tool(lay0)', function (obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                var tr = obj.tr;
                var nextTr = tr.next();
                if (nextTr.hasClass('collapsible-content')) {
                    // 如果已经展开，就折叠当前行的子表格
                    nextTr.remove();
                } else {
                    // 如果未展开，就展开当前行的子表格
                    var subTableId = `subTable-${data.dataid}`;
                    var content = `<tr class="collapsible-content collapsible-row"><td colspan="9" style='padding-bottom: 10px; '><table style="margin-bottom: 5px;margin-top: 5px;width:90%" id="${subTableId}" lay-filter="${subTableId}-filter"></table></td></tr>`;
                    tr.after(content);

                    // 渲染子表格
                    table.render({
                        elem: `#${subTableId}`,
                        cols: [[
                            { field: 'dataid', title: '任务ID', width: 80 },
                            { field: 'rowId', title: '子任务ID', width: 80 },
                            { field: 'status', title: '状态', width: 120, templet: function (d) {
                                if (d.status === "success") {
                                    return '<span style="color: green;">' + d.status + '</span>';
                                } else if (d.status === "fail") {
                                    return '<span style="color: red;">' + d.status + '</span>';
                                }
                            } },
                            { field: 'start', title: '开始时间', width: 200 },
                            { field: 'end', title: '结束时间', width: 200 },
                            { field: 'filename', title: '文件名', width: 300 },
                            { field: 'result', title: '结果', width: 150 },
                            { field: 'lossInfo', title: '详情', width: 250 },
                            { field: 'operate', title: '操作', width: 150, toolbar: '#bardown1' },
                        ]],
                        data: data.subData,
                        done: function() {
                            // 确保子表格渲染完成后显示
                            $(`#${subTableId}`).closest('.collapsible-content').show();
                        }
                    });

                    // 监听子表格工具条
                    table.on(`tool(${subTableId}-filter)`, function (obj) {
                        console.log(obj);
                        switch (obj.event) {
                            case 'start1':
                                start1(obj.data.dataid, obj.data.rowId);
                                break;
                        }
                    });
                }
            }
        });
    }

    // 初始化语音Log文件上传功能
    function initVoiceLogUpload() {
        var selectedWngFiles = [];
        var selectedAudioFiles = [];

        // WNG文件选择
        upload.render({
            elem: '#selectWngFilesBtn',
            url: '', // 这里不设置url，因为我们要手动处理
            auto: false, // 不自动上传
            multiple: true, // 允许多选
            accept: 'file', // 允许所有文件类型
            exts: 'apm|dlf|saf|wng', // 允许的文件扩展名
            choose: function(obj) {
                var files = obj.pushFile();
                var validFiles = [];
                var invalidFiles = [];

                // 验证每个文件的命名格式
                Object.keys(files).forEach(function(index) {
                    var file = files[index];
                    var fileName = file.name;

                    if (validateWngFileName(fileName)) {
                        validFiles.push(file);
                        selectedWngFiles.push(file);
                    } else {
                        invalidFiles.push(fileName);
                    }
                });

                // 如果有无效文件，显示错误信息
                if (invalidFiles.length > 0) {
                    var errorMsg = 'WNG文件命名格式错误：<br/>' +
                                 invalidFiles.join('<br/>') +
                                 '<br/><br/>正确格式：AI智能打卡测试-广州中兴-VONR/VOLTE-主叫/被叫.apm<br/>' +
                                 '要求：以"-"分割，第三个是VONR或VOLTE，第四个是主叫或被叫';
                    layer.msg(errorMsg, {icon: 2, time: 8000});
                }

                updateFileCount('#wngFileCount', selectedWngFiles.length, 'WNG');
                updateUploadButton();
            }
        });

        // Audio文件选择
        upload.render({
            elem: '#selectAudioFilesBtn',
            url: '', // 这里不设置url，因为我们要手动处理
            auto: false, // 不自动上传
            multiple: true, // 允许多选
            accept: 'audio', // 音频文件
            exts: 'mp3|wav|m4a|aac|flac|ogg|wma', // 音频文件扩展名
            choose: function(obj) {
                var files = obj.pushFile();
                var validFiles = [];
                var invalidFiles = [];

                // 验证每个文件的命名格式
                Object.keys(files).forEach(function(index) {
                    var file = files[index];
                    var fileName = file.name;

                    if (validateAudioFileName(fileName)) {
                        validFiles.push(file);
                        selectedAudioFiles.push(file);
                    } else {
                        invalidFiles.push(fileName);
                    }
                });

                // 如果有无效文件，显示错误信息
                if (invalidFiles.length > 0) {
                    var errorMsg = 'Audio文件命名格式错误：<br/>' +
                                 invalidFiles.join('<br/>') +
                                 '<br/><br/>正确格式：广州中兴_17344224465_20250508110536.m4a<br/>' +
                                 '要求：以"_"分割，第二个是电话号码，第三个是时间';
                    layer.msg(errorMsg, {icon: 2, time: 8000});
                }

                updateFileCount('#audioFileCount', selectedAudioFiles.length, 'Audio');
                updateUploadButton();
            }
        });

        // 更新文件计数显示
        function updateFileCount(selector, count, type) {
            if (count > 0) {
                $(selector).text('已选择 ' + count + ' 个' + type + '文件')
                    .css('color', '#5FB878')
                    .addClass('selected');
            } else {
                $(selector).text('未选择文件')
                    .css('color', '#666')
                    .removeClass('selected');
            }
        }

        // 更新上传按钮状态
        function updateUploadButton() {
            if (selectedWngFiles.length > 0 && selectedAudioFiles.length > 0) {
                $('#uploadFilesBtn').removeClass('layui-btn-primary').addClass('layui-btn-normal').prop('disabled', false);
            } else {
                $('#uploadFilesBtn').removeClass('layui-btn-normal').addClass('layui-btn-primary').prop('disabled', true);
            }
        }

        // 上传文件
        $('#uploadFilesBtn').on('click', function() {
            // 验证文件选择
            if (selectedWngFiles.length === 0 || selectedAudioFiles.length === 0) {
                layer.msg('请先选择WNG文件和Audio文件', {icon: 2});
                return;
            }

            // 再次验证所有文件名格式
            var invalidWngFiles = [];
            var invalidAudioFiles = [];

            // 验证WNG文件
            for (var i = 0; i < selectedWngFiles.length; i++) {
                if (!validateWngFileName(selectedWngFiles[i].name)) {
                    invalidWngFiles.push(selectedWngFiles[i].name);
                }
            }

            // 验证Audio文件
            for (var i = 0; i < selectedAudioFiles.length; i++) {
                if (!validateAudioFileName(selectedAudioFiles[i].name)) {
                    invalidAudioFiles.push(selectedAudioFiles[i].name);
                }
            }

            // 如果有无效文件，阻止上传
            if (invalidWngFiles.length > 0 || invalidAudioFiles.length > 0) {
                var errorMsg = '文件命名格式错误，无法上传：<br/>';
                if (invalidWngFiles.length > 0) {
                    errorMsg += '<br/><strong>WNG文件：</strong><br/>' + invalidWngFiles.join('<br/>');
                }
                if (invalidAudioFiles.length > 0) {
                    errorMsg += '<br/><strong>Audio文件：</strong><br/>' + invalidAudioFiles.join('<br/>');
                }
                errorMsg += '<br/><br/>请检查文件命名格式后重新选择文件。';
                layer.msg(errorMsg, {icon: 2, time: 10000});
                return;
            }

            // 获取文件摘要信息
            var summary = getFilesSummary(selectedWngFiles, selectedAudioFiles);
            console.log('文件上传摘要:', summary);

            // 显示确认对话框
            var confirmMsg = '即将上传：<br/>' +
                           'WNG文件: ' + summary.wngCount + '个<br/>' +
                           'Audio文件: ' + summary.audioCount + '个<br/>' +
                           '总大小: ' + summary.totalSizeFormatted + '<br/>' +
                           '是否继续？';

            layer.confirm(confirmMsg, {
                icon: 3,
                title: '确认上传'
            }, function(index) {
                layer.close(index);
                performUpload();
            });
        });

        // 执行上传
        function performUpload() {
            // 显示加载层和进度条
            var loadingIndex = layer.load(1, {
                shade: [0.3, '#000']
            });
            showProgress();

            // 获取登录信息
            var loginInfo = islogininfo();
            var loginParts = loginInfo.split("#");
            var employeeId = loginParts[0];
            var userName = loginParts[1];
            var token = loginParts[2];

            console.log('登录信息:', {employeeId: employeeId, userName: userName, token: token});

            // 创建FormData
            var formData = new FormData();
            formData.append('employee_id', employeeId);
            formData.append('token', token);

            // 添加WNG文件
            for (var i = 0; i < selectedWngFiles.length; i++) {
                formData.append('wng_files', selectedWngFiles[i]);
            }

            // 添加Audio文件
            for (var i = 0; i < selectedAudioFiles.length; i++) {
                formData.append('audio_files', selectedAudioFiles[i]);
            }

            // 调用后台Servlet
            $.ajax({
                url: 'ApiUploadServlet',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                timeout: 600000, // 5分钟超时
                success: function(result) {
                    layer.close(loadingIndex);
                    hideProgress();

                    if (result.code === 0) {
                        layer.msg('上传成功！任务ID: ' + result.task_id, {icon: 1, time: 4000});

                        // 清空选择的文件
                        selectedWngFiles = [];
                        selectedAudioFiles = [];
                        updateFileCount('#wngFileCount', 0, 'WNG');
                        updateFileCount('#audioFileCount', 0, 'Audio');
                        updateUploadButton();

                        // 刷新上传结果记录表格
                        if (window.log2TableRendered) {
                            table.reload('testReload2');
                        } else {
                            renderLog2Table();
                            window.log2TableRendered = true;
                        }
                    } else {
                        layer.msg('上传失败：' + (result.msg || '未知错误'), {icon: 2, time: 5000});
                    }
                },
                error: function(xhr, status, error) {
                    layer.close(loadingIndex);
                    hideProgress();

                    var errorMsg = '上传失败：';
                    if (status === 'timeout') {
                        errorMsg += '请求超时';
                    } else if (xhr.status === 0) {
                        errorMsg += '网络连接失败';
                    } else {
                        errorMsg += 'HTTP ' + xhr.status + ' - ' + error;
                    }

                    console.error('上传错误:', xhr, status, error);
                    layer.msg(errorMsg, {icon: 2, time: 5000});
                }
            });
        }

        // 获取文件摘要信息
        function getFilesSummary(wngFiles, audioFiles) {
            var wngCount = wngFiles ? wngFiles.length : 0;
            var audioCount = audioFiles ? audioFiles.length : 0;

            var totalSize = 0;
            if (wngFiles) {
                for (var i = 0; i < wngFiles.length; i++) {
                    totalSize += wngFiles[i].size;
                }
            }
            if (audioFiles) {
                for (var i = 0; i < audioFiles.length; i++) {
                    totalSize += audioFiles[i].size;
                }
            }

            return {
                wngCount: wngCount,
                audioCount: audioCount,
                totalSize: totalSize,
                totalSizeFormatted: formatFileSize(totalSize)
            };
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes < 1024) {
                return bytes + ' B';
            } else if (bytes < 1024 * 1024) {
                return (bytes / 1024).toFixed(1) + ' KB';
            } else if (bytes < 1024 * 1024 * 1024) {
                return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
            } else {
                return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
            }
        }
    }

    // WNG文件名验证函数
    function validateWngFileName(fileName) {
        // 移除文件扩展名
        var nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));

        // 以"-"分割文件名
        var parts = nameWithoutExt.split('-');

        // 检查是否至少有4个部分
        if (parts.length < 4) {
            return false;
        }

        // 第三个部分应该是VONR或VOLTE
        var thirdPart = parts[2].trim().toUpperCase();
        if (thirdPart !== 'VONR' && thirdPart !== 'VOLTE') {
            return false;
        }

        // 第四个部分应该是主叫或被叫
        var fourthPart = parts[3].trim();
        if (fourthPart !== '主叫' && fourthPart !== '被叫') {
            return false;
        }

        return true;
    }

    // Audio文件名验证函数
    function validateAudioFileName(fileName) {
        // 移除文件扩展名
        var nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));

        // 以"_"分割文件名
        var parts = nameWithoutExt.split('_');

        // 检查是否至少有3个部分
        if (parts.length < 3) {
            return false;
        }

        // 第二个部分应该是电话号码（11位数字）
        var phonePart = parts[1].trim();
        if (!/^\d{11}$/.test(phonePart)) {
            return false;
        }

        // 第三个部分应该是时间格式（14位数字，格式：YYYYMMDDHHMMSS）
        var timePart = parts[2].trim();
        if (!/^\d{14}$/.test(timePart)) {
            return false;
        }

        // 验证时间格式的合理性
        var year = parseInt(timePart.substring(0, 4));
        var month = parseInt(timePart.substring(4, 6));
        var day = parseInt(timePart.substring(6, 8));
        var hour = parseInt(timePart.substring(8, 10));
        var minute = parseInt(timePart.substring(10, 12));
        var second = parseInt(timePart.substring(12, 14));

        if (year < 2020 || year > 2030 ||
            month < 1 || month > 12 ||
            day < 1 || day > 31 ||
            hour < 0 || hour > 23 ||
            minute < 0 || minute > 59 ||
            second < 0 || second > 59) {
            return false;
        }

        return true;
    }

    // 搜索功能
    function search() {
        var demoReload = document.getElementById("tag0").value;
        table.reload('testReload0', {
            url: "QueryVonrTableSearchServlet2",
            request: {
                pageName: 'pageIndex',  // 页码的参数名称，默认：page
                limitName: 'pageSize', // 每页数据量的参数名，默认：limit
            },
            response: {
                statusName: 'code',     // 规定数据状态的字段名称，默认：code
                statusCode: 0,          // 规定成功的状态码，默认：0
                countName: 'count', // 规定数据总数的字段名称，默认：count
                msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
            },
            page: {
                layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                curr: 1,      // 设置默认起始页1
                groups: 10,   //只显示10个连续页码,就是说显示10个可见页其他的省略
                first: false, // 不显示首页
                last: false   // 不显示尾页
            },
            limit: 20,
            limits: [5, 10, 15, 20, 100, 1000],
            where: {
                search_data: encodeURI(demoReload),
            }
        }, 'data');
        document.getElementById("tag0").value = demoReload;
    }

    // 开始操作
    function start1(dataid, rowid) {
        var url = "https://iask.zte.com.cn/api/whisper/audio/" + dataid + "/" + rowid + "";
        layer.open({
            type: 2,
            title: false,
            area: ['30%', '30%'],
            content: url
        });
    }

    // 查询并跳转操作
    function queryAndJump(data) {
        console.log("查询并跳转:", data);

        // 显示加载层
        layer.load();

        // 调用查询接口
        $.ajax({
            url: 'QueryVonrDetailServlet',
            type: 'GET',
            data: {
                dataid: data.taskid,
            },
            dataType: 'json',
            success: function(res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    // 跳转到详情页面，传递查询结果
                    var reporturl =  res.result.report_url;

                    if(reporturl!="" && reporturl!=null){
	                    window.open(reporturl, '_blank');
	                    table.reload('testReload2');
					}else{
						layer.msg("暂无分析报告生成", {icon: 2});
						table.reload('testReload2');
					}
                } else {
                    layer.msg( (res.msg || '未知错误'), {icon: 2});
                    table.reload('testReload2');
                }
            },
            error: function() {
                layer.closeAll('loading');
                layer.msg('查询请求异常', {icon: 2});
            }
        });
    }

    // 显示进度条
    function showProgress() {
        $('#uploadProgress').show();
        updateProgress(0);
    }

    // 隐藏进度条
    function hideProgress() {
        $('#uploadProgress').hide();
        updateProgress(0);
    }

    // 更新进度条
    function updateProgress(percent) {
        $('#uploadProgress .layui-progress-bar').attr('lay-percent', percent + '%');
    }

    // 监听主表格工具栏事件
    table.on('toolbar(lay0)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event) {
            case 'search':
                search();
                break;
        }
    });

    // 监听aivonrlog2表格行工具条事件
    table.on('tool(lay2)', function (obj) {
        var data = obj.data;
        console.log(data);
        switch (obj.event) {
            case 'query':
                queryAndJump(data);
                break;
        }
    });

    // 监听tab切换事件
    element.on('tab(mainTab)', function(data){
        console.log('切换到第' + (data.index + 1) + '个tab');
        if(data.index === 1) {
            // 切换到语音Log上传tab时，如果表格还没有渲染，则渲染它
            if(!window.log2TableRendered) {
                setTimeout(function() {
                    renderLog2Table();
                    window.log2TableRendered = true;
                }, 200);
            }
        }
    });

    // 初始化主表格
    renderMainTable();

    // 初始化语音Log文件上传功能
    initVoiceLogUpload();

    // 标记表格还未渲染
    window.log2TableRendered = false;

});


function islogininfo(){
	var qqq="",www="",name="",token="";
	var idss = document.cookie.split(";");
	for(var i=0;i< idss.length;i++){
		var xx = idss[i];
		qqq=xx.split("=")[0];
		qqq=qqq.trim();
		if(qqq=="info")
		{
			www=xx.split("=")[1].split("#")[0];
			name=xx.split("=")[1].split("#")[1];
			token=xx.split("=")[1].split("#")[2];

		}
	}
//		www = "10306639";
//		name="陶森";
//		token="72fbd8924d91aa52f9383328f424b139";
	return www+"#"+name+"#"+token;
}
