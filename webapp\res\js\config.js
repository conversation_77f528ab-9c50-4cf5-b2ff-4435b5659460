function sleep(ms){
	let start = Date.now();
	let end = start + ms;
	while(true){
		if(Date.now()> end){
			return
		}
	}
}

var xmlHttp = false;
function createXMLHttpRequest() {
	if (window.ActiveXObject) {
		xmlHttp = new ActiveXObject("Microsoft.XMLHTTP");
	}
	else if (window.XMLHttpRequest) {
		xmlHttp = new XMLHttpRequest();
	}
}

function addfeature()
{
	var gnbid  =  document.getElementById( "gnbidinfo" ).value; 
	var pci  =  document.getElementById( "pciinfo" ).value; 
	var feature  =  document.getElementById( "featureinfo" ).innerText; 
    if(gnbid==""||pci==""||feature==""){
		alert("网元/功能/小区为空");
	}else{
		var devname1=document.getElementById("result")
	    if (devname1.style.display=="none"){
			devname1.style.display="inline-block"
		}
		gnbid=gnbid.split(";");pci=pci.split(";");
		var d =$("#paramresult tr").length;
		for(var i=0;i<gnbid.length;i++){
				var tr = document.createElement("tr");
				var td1 = document.createElement("td");
				td1.innerText=gnbid[i];
				var td2 = document.createElement("td");
				td2.innerText=pci[i];
				var td3 = document.createElement("td");
				td3.innerText=feature;
				var td4 = document.createElement("td");
				var td4neirong = "<input id='"+(d+i+1)+"' value='"+(d+i+1)+"' checked type='checkbox'>";
				td4.innerHTML=td4neirong;
				tr.appendChild(td1);
				tr.appendChild(td2);
				tr.appendChild(td3);
				tr.appendChild(td4);
				document.getElementById("paramresult").appendChild(tr);
		}
		document.getElementById("queren").style.display="block";
	}
    document.getElementById("paramjuti").style.display="none";
}

function config()
{
	var t = document.getElementById( "paramresult" );
	var gnbids="",pcis="",features="";
	var rows = t.rows;
	for(var i=0;i<rows.length;i++){
		ischeck = document.getElementById((i+1)).checked;	
		if(ischeck==false) continue;
		for(var j=0;j<rows[i].cells.length;j++){

			if(j==0){
				if(i==rows.length-1){
					gnbids = gnbids+rows[i].cells[j].innerText;
				}else{
					gnbids = gnbids+rows[i].cells[j].innerText+"#";
				}
				
			}else if(j==1){
				if(i==rows.length-1){
					pcis = pcis+rows[i].cells[j].innerText;
				}else{
					pcis = pcis+rows[i].cells[j].innerText+"#";
				}
				
			}else if(j==2){
				if(i==rows.length-1){
					features = features+rows[i].cells[j].innerText;
				}else{
					features = features+rows[i].cells[j].innerText+"#";
				}
				
			}
		}
	}
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = configcallBack; 
    gnbids=encodeURIComponent(gnbids);
    pcis=encodeURIComponent(pcis);
    features=encodeURIComponent(features);
	document.getElementById("loading").style.display = 'block';

    var url=encodeURI("ConfigFeature?gnbid="+gnbids+"&feature="+features+"&pci="+pcis);

    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}


function configcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);	
			document.getElementById("loading").style.display = 'none';
			var t = document.getElementById( "paramresult" );
			var s ="";
			for(var i =0;i<data.length;i++){
				s+="<tr>"
				s+="<td title='"+data[i].gnbid+"' style='width:10%; overflow:hidden;word-break:keep-all;white-space:nowrap;text-align:center;'>"+data[i].gnbid+"</td>"
				s+="<td title='"+data[i].pci+"' style='width:10%;overflow:hidden;word-break:keep-all;white-space:nowrap;text-align:center;'>"+data[i].pci+"</td>"
				s+="<td title='"+data[i].featurename+"' style='width:55%;overflow:hidden;word-break:keep-all;white-space:nowrap;text-align:center;'>"+data[i].featurename+"</td>"
				s+="<td title='"+data[i].message+"' style='width:15%;overflow:hidden;word-break:keep-all;white-space:nowrap;text-align:center;'>"+data[i].message+"</td>"
				s+="<td  style='width:10%;overflow:hidden;word-break:keep-all;white-space:nowrap;text-align:center;'  onclick='queryconfiginfo()'>"+"查看"+"</td>"
				s+="</tr>"
			}
			t.innerHTML=s;
			document.getElementById("queren").style.display="none";
		}
	}
}

function add_row(gnbid,pci,feature){
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = configinfoquerycallBack;
    document.getElementById("loading").style.display = 'block';
    var url=encodeURI("ConfigInfoQuery?gnbid="+gnbid+"&feature="+feature +"&pci="+pci);
    url=encodeURI(url); 
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}

function configinfoquerycallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);	
			document.getElementById("loading").style.display = 'none';
			if(data[0].length==0){
				alert("未找到适配参数套");
			}else {
				var d =$("#paramresult tr").length;
			for(var i=0;i<data[0].length;i++){
				var tr = document.createElement("tr");
				var td1 = document.createElement("td");
				td1.innerText=data[0][i].gnbid;
				var td2 = document.createElement("td");
				td2.innerText=data[0][i].pci;
				var td3 = document.createElement("td");
				td3.innerText=data[0][i].featurename;
				var td4 = document.createElement("td");
				var td4neirong = "<input id='"+(d+i+1)+"' value='"+(d+i+1)+"' checked type='checkbox'>";
				td4.innerHTML=td4neirong;
				tr.appendChild(td1);
				tr.appendChild(td2);
				tr.appendChild(td3);
				tr.appendChild(td4);
				document.getElementById("paramresult").appendChild(tr);
			}
			}

		}
	}
}


function queryconfiginfo()
{
	var td = event.srcElement;
	tr =td.parentElement; 
	var gnbid=tr.cells[0].innerText;
	var pci = tr.cells[1].innerText;
	var feature = tr.cells[2].innerText;
	gnbid=encodeURIComponent(gnbid);
	pci=encodeURIComponent(pci);
	feature=encodeURIComponent(feature);
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = configinfocallBack; 
//    
    var url=encodeURI("ConfigInfo?gnbid="+gnbid+"&feature="+feature +"&pci="+pci);
//    url=encodeURI(url); 
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}

function configinfo()
{
	var gnbid  =  document.getElementById( "gnbidinfo" ).value; 
	var pci  =  document.getElementById( "pciinfo" ).value; 
	var feature  =  document.getElementById( "featureinfo" ).innerText; 
    if(gnbid==""||pci==""||feature==""){
		alert("网元/功能/小区为空");
	}else{
		var devname1=document.getElementById("result")
	    if (devname1.style.display=="none"){
			devname1.style.display="inline-block"
		}
		gnbid=gnbid.split(";");pci=pci.split(";");
		for(var i=0;i<gnbid.length;i++){
			add_row(gnbid[i],pci[i],feature);
		}
		document.getElementById("queren").style.display="block";
	}
    document.getElementById("paramjuti").style.display="none";
}


function configinfocallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);	
			if(data.code==1){
					var devname1=document.getElementById("result")
					if (devname1.style.display=="inline-block"){
						devname1.style.display="none"
					}
					alert(data.message);
			}else{
				generate(data.list);
			}
		}
	}
}


function generate(data){
	
	var result=document.getElementById("paramjuti")
	if (result.style.display=="none"){
		result.style.display="inline-block"
	}
	
	var backfilename=document.getElementById("backfilename")
	if (backfilename.style.display=="inline-block"){
		backfilename.style.display="none"
	}
		
	var div = document.getElementById("paramjutiresult");
	var str='';
	var str="<thead><th>mocname</th><th>paramname</th><th>paramvalue</th><th>recommendvalue</th><th>adjust</th><th>remark</th></thead>";
	str+="<tbody>";	
	for (var i = 0;i<data.length;i++){
		var color = (data[i].isconsistent=='0')?"#FFC0CB":"#F7F7F7";
		if(data[i].remark == "add" || data[i].remark == "delete"){
			color = "#F7F7F7";
		}
			str+="<tr bgcolor='"+color+"'>";
			str+='<td>'+data[i].mocname+'</td>';
			str+='<td>'+data[i].paramname+'</td>';
			str+='<td>'+data[i].paramvalue+'</td>';
			str+='<td>'+data[i].recommendvalue+'</td>';
			str+='<td>'+data[i].adjustp+'</td>';
			str+='<td>'+data[i].remark+'</td>';
			str+='</tr>';	
	}
	str+="</tbody>";	
	div.innerHTML = str;
}

function recover() {
		var filename  =  document.getElementById( "filename" ).value; 

		createXMLHttpRequest();
	    xmlHttp.onreadystatechange = recovercallBack; 
	    var url=encodeURI("ConfigRecover?filename="+filename);
	    url=encodeURI(url); 
	    xmlHttp.open("POST", url, true);
	    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
	    xmlHttp.send(null);	
			
	
}

function recovercallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);	
			if(data.code==0){
				alert(data.message);
			}else{
				alert(data.message);
			}
			}
	}
}


function backup() {
	var gnbid  =  document.getElementById( "gnbidinfo" ).value; 
	if(gnbid==""){
		alert("请输入网元ID");
	}
	else{
		document.getElementById("loading").style.display = 'block';
		createXMLHttpRequest();
	    xmlHttp.onreadystatechange = backupcallBack; 
	//    alert(gnbid);
	    var url=encodeURI("ConfigBackup?gnbid="+gnbid);
	    url=encodeURI(url); 
	    xmlHttp.open("POST", url, true);
	    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
	    xmlHttp.send(null);	
	}

}

function backupcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			document.getElementById("loading").style.display = 'none';
			var data=xmlHttp.responseText;
			data = JSON.parse(data);	
			
			if(data.code==0){
				alert(data.message);
			}else{
				alert(data.message);
			}
			}
	}
}



function querybackfile(){
	document.getElementById( "result" ).style.display="none";
	document.getElementById( "fieldset" ).style.display="none";

	var gnbid  =  document.getElementById( "gnbidinfo" ).value; 
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = querybackfilecallBack; 
    var url=encodeURI("ConfigBackupfilenameinfo?gnbid="+gnbid);
    url=encodeURI(url); 
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}

function querybackfilecallBack(){
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);	
			if(data.code == 0){
				generatefilename(data.listfilename);
			}else{
				alert(data.message);
			}
		}
	}
}

function generatefilename(data){
	
	var backfilename=document.getElementById("backfilename")
	if (backfilename.style.display=="none"){
		backfilename.style.display="inline-block"
	}
	var result=document.getElementById("result")
	if (result.style.display=="inline-block"){
		result.style.display="none"
	}
		
	var div = document.getElementById("backfilenameresult");
	var str='';
	var str="<thead><th>filename</th><th>updatetime</th></thead>";
	str+="<tbody>";	
	for (var i = 0;i<data.length;i++){
		str+="<tr>";
		str+='<td>'+data[i].filename+'</td>';
		str+='<td>'+data[i].updatetime+'</td>';
		str+='</tr>';		
	}
	str+="</tbody>";	
	div.innerHTML = str;
}

function clean(){
	document.getElementById("paramresult").innerHTML="";
}

