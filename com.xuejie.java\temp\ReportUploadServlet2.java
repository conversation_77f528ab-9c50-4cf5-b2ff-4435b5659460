package temp;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;

/**
 * Servlet implementation class ReportUploadServlet
 */
@WebServlet("/ReportUploadServlet2")
public class ReportUploadServlet2 extends HttpServlet {
    private static final long serialVersionUID = 1L;

    // 濞戞挸锕ｇ槐鍫曞棘閸ワ附顐藉ǎ鍥ㄧ箓閻°劑鎯勯鑲╃Э
    private static final String UPLOAD_DIRECTORY = "uploads";
    // 濞戞挸锕ｇ槐鍫曟煀瀹ュ洨鏋�
    private static final int MEMORY_THRESHOLD = 1024 * 1024 * 3;  // 3MB
    private static final int MAX_FILE_SIZE = 1024 * 1024 * 2000; // 2000MB
    private static final int MAX_REQUEST_SIZE = 1024 * 1024 * 4000; // 4000MB
    
    // API闂佹澘绉堕悿锟� - 閻犲洭鏀遍悧鎾箲椤旇偐鏉介梻鍕噺閸庡繘宕橀崗鍛婂弿闁猴拷閿燂拷
    private static final String API_BASE_URL = "http://10.231.145.183:10888"; // 閻犲洩娓归幈銊╁绩闁稖绀嬮悗鍦仱濡绢垶鎯冮崙姗甀闁革附婢樺锟�
    private static final String API_UPLOAD_PATH = "/api/report/upload/files";

    /**
     * @see HttpServlet#HttpServlet()
     */
    public ReportUploadServlet2() {
        super();
    }

    /**
     * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        doPost(request, response);
    }

    /**
     * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        request.setCharacterEncoding("UTF-8");

        JSONObject result = new JSONObject();
        
        // 婵☆偓鎷烽柡灞诲劥椤曨剙效閸屾稒笑闁告熬缂氱拹鐒焨ltipart/form-data
        if (!ServletFileUpload.isMultipartContent(request)) {
            result.put("code", 1);
            result.put("msg", "");
            writeResponse(response, result);
            return;
        }

        try {
            // 闂佹澘绉堕悿鍡樼▔婵犱胶鐐婇柛娆忓�归弳锟�
            DiskFileItemFactory factory = new DiskFileItemFactory();
            factory.setSizeThreshold(MEMORY_THRESHOLD);
            factory.setRepository(new File(System.getProperty("java.io.tmpdir")));

            ServletFileUpload upload = new ServletFileUpload(factory);
            upload.setFileSizeMax(MAX_FILE_SIZE);
            upload.setSizeMax(MAX_REQUEST_SIZE);
            upload.setHeaderEncoding("UTF-8");

            // 閻熸瑱绲鹃悗鐣屾嫚闁垮婀撮柣銊ュ閸炲锟界顫夎ぐ渚�宕ｉ弽銊︾�ù鐘哄煐閺嗙喖骞戦敓锟�
            List<FileItem> formItems = upload.parseRequest(request);

            String number = "";
            String token = "";
            List<File> wngFiles = new ArrayList<>();
            List<File> audioFiles = new ArrayList<>();
            List<String> wngFileNames = new ArrayList<>();
            List<String> audioFileNames = new ArrayList<>();

            if (formItems != null && formItems.size() > 0) {
                // 闁兼儳鍢茶ぐ鍥ㄧ▔婵犱胶鐐婇柣鈺婂枛缂嶅秹鎯冮崟顓犲崪閻庣數顢婇惌鎯ь嚗閿燂拷
                String uploadPath = getServletContext().getRealPath("") + File.separator + UPLOAD_DIRECTORY;
                File uploadDir = new File(uploadPath);
                if (!uploadDir.exists()) {
                    uploadDir.mkdir();
                }

                for (FileItem item : formItems) {
                    if (item.isFormField()) {
                        // 濠㈣泛瀚幃濠勬偘閵娿儱绀嬮悗娑欘殕椤旓拷
                        String fieldName = item.getFieldName();
                        String fieldValue = item.getString("UTF-8");
                        
                        if ("number".equals(fieldName)) {
                            number = fieldValue;
                        } else if ("token".equals(fieldName)) {
                            token = fieldValue;
                        }
                    } else {
                        // 濠㈣泛瀚幃濠囧棘閸ワ附顐介悗娑欘殕椤旓拷
                        String fieldName = item.getFieldName();
                        String fileName = item.getName();
                        
                        if (fileName != null && !fileName.isEmpty()) {
                            // 婢跺嫮鎮婃稉顓熸瀮閺傚洣娆㈤崥宥勮础閻線妫舵０锟�
                            fileName = fixChineseFileName(fileName);

                            // 瀵规枃浠跺悕杩涜URL缂栫爜
                            String encodedFileName = encodeFileName(fileName);

                            // 鐢熸垚鏂扮殑鏂囦欢鍚�
                            String newFileName = generateFileName() + "_" + encodedFileName;
                            String filePath = uploadPath + File.separator + newFileName;
                            File storeFile = new File(filePath);

                            // 濞ｅ洦绻傞悺銊╁棘閸ワ附顐介柛鎺撳閳ユ牠鎯勯敓锟�
                            item.write(storeFile);

                            if ("wng_files".equals(fieldName)) {
                                wngFiles.add(storeFile);
                                // 璁板綍鍘熷鏂囦欢鍚嶏紙鐢ㄤ簬鏄剧ず锛夊拰缂栫爜鍚庣殑鏂囦欢鍚嶏紙鐢ㄤ簬瀛樺偍锛�
                                wngFileNames.add(fileName + " [瀛樺偍涓�: " + newFileName + "]");
                            } else if ("audio_files".equals(fieldName)) {
                                audioFiles.add(storeFile);
                                // 璁板綍鍘熷鏂囦欢鍚嶏紙鐢ㄤ簬鏄剧ず锛夊拰缂栫爜鍚庣殑鏂囦欢鍚嶏紙鐢ㄤ簬瀛樺偍锛�
                                audioFileNames.add(fileName + " [瀛樺偍涓�: " + newFileName + "]");
                            }
                        }
                    }
                }

                // 濡ょ姴鐭侀惁澶庣疀閸涢偊娲ｉ柛娆忓�归弳锟�
                if (number.isEmpty() || token.isEmpty()) {
                    result.put("code", 1);
                    result.put("msg", "璇风櫥褰�");
                    writeResponse(response, result);
                    return;
                }

                if (wngFiles.isEmpty() || audioFiles.isEmpty()) {
                    result.put("code", 1);
                    result.put("msg", "璇烽�夋嫨wng_files/audio_files");
                    writeResponse(response, result);
                    return;
                }

                // 閻犲鍟伴弫銈嗙▔婵犱胶鐐婇柟鎭掑劚瑜帮拷
                String apiResult = callUploadApi(wngFiles, audioFiles, number, token);
                JSONObject apiResponse = new JSONObject(apiResult);
                
                if (!apiResponse.isNull("task_id")) {
                    String taskid = String.valueOf(apiResponse.getInt("task_id")) ;
                    
                    // 闁圭粯甯掗崣鍡涘礆閻х敹vonrlog2闁轰胶澧楀畵浣烘偘閿燂拷
                    insertToAivonrlog2(taskid, number, wngFileNames, audioFileNames);
                    
                    result.put("code", 0);
                    result.put("msg", "涓婁紶鎴愬姛,task_id:" + taskid);
                    result.put("taskid", taskid);
                } else {
                    result.put("code", 1);
                    result.put("msg", "涓婁紶寮傚父:" + apiResult);
                }

            } else {
                result.put("code", 1);
                result.put("msg", "");
            }
        } catch (Exception ex) {
            result.put("code", 1);
            result.put("msg", "" + ex.getMessage());
            ex.printStackTrace();
        }

        writeResponse(response, result);
    }

    /**
     * 闁汇垻鍠愰崹姘舵⒕韫囨梹绨氶柡鍌氭矗濞嗐垽宕ラ敓锟�
     */
    private String generateFileName() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = new Date();
        String str = simpleDateFormat.format(date);
        
        int rannum = (int)(Math.random() * 9000) + 1000;
        return str + "_" + rannum;
    }

    /**
     * 閻犲鍟伴弫銈嗙▔婵犱胶鐐婇柟鎭掑劚瑜帮拷
     */
    private String callUploadApi(List<File> wngFiles, List<File> audioFiles, String number, String token) {
        try {
            // 闁哄秷顫夊畵涔rl缂侊拷鏉炴壆浼愰悹瀣暞閺嗩枦RL
            String url = API_BASE_URL + API_UPLOAD_PATH;

            // 闁哄瀚紓鎾舵嫚闁垮婀� - 濞达綀娉曢弫銈咁潰閿濆洠锟芥﹢鎯冮崚鍒礽rest API
            com.mashape.unirest.request.body.MultipartBody multipartBody = Unirest.post(url)
                .header("accept", "application/json")
                .field("employee_id", number)  // 闁哄秷顫夊畵涔rl缂侊拷鏉炴壆浼愬ù锝堟硶閺侇構mployee_id
                .field("token", token);
            
            // 婵烇綀顕ф慨婵縉G闁哄倸娲ｅ▎锟�
            for (File wngFile : wngFiles) {
                multipartBody = multipartBody.field("wng_files", wngFile);
            }
            
            // 婵烇綀顕ф慨婵dio闁哄倸娲ｅ▎锟�
            for (File audioFile : audioFiles) {
                multipartBody = multipartBody.field("audio_files", audioFile);
            }
            
            // 闁圭瑳鍡╂斀閻犲洭鏀遍惇锟�
            HttpResponse<JsonNode> httpResponse = multipartBody.asJson();
                
            if (httpResponse.getStatus() == 200) {
                JSONObject responseObject = httpResponse.getBody().getObject();
                return responseObject.toString();
            } else {
                return "{\"error\":\"" + httpResponse.getStatus() + "\"}";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"error\":\"" + e.getMessage() + "\"}";
        }
    }

    /**
     * 闁圭粯甯掗崣鍡涘极閻楀牆绁﹂柛鎺旀珮ivonrlog2閻炴冻鎷�
     */
    private void insertToAivonrlog2(String taskid, String userid, List<String> wngFileNames, List<String> audioFileNames) {
        try {
            Date d = new Date();
            SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String uploadtime = sbf.format(d);

            // 闁哄瀚紓鎾诲棘閸ワ附顐藉ǎ鍥ｅ墲娴煎懐锟芥稒顨堥浣圭▔閿燂拷
            StringBuilder fileInfo = new StringBuilder();
            fileInfo.append("WNG鏂囦欢:");
            for (int i = 0; i < wngFileNames.size(); i++) {
                if (i > 0) fileInfo.append(", ");
                fileInfo.append(wngFileNames.get(i));
            }
            fileInfo.append("; Audio鏂囦欢:");
            for (int i = 0; i < audioFileNames.size(); i++) {
                if (i > 0) fileInfo.append(", ");
                fileInfo.append(audioFileNames.get(i));
            }

            DaoCMCC dao = new DaoCMCC();
            // 閺夆晜鐟╅崳閿嬫媴鐠恒劍鏆忕紒鐙呮嫹闁告娲滃▓鎱箈ecute闁哄倽顫夌涵鍫曟晬鐏炵瓔娲ら柡瀣矒濞撳墎鎲版稉纭唀paredStatement闁告瑯鍨禍鎺楀箥閳轰胶娼擠aoCMCC缂侇偓鎷�
            String insertSql = "INSERT INTO aivonrlog2(userid, uploadtime, status, taskid, filename) VALUES('" + userid + "', '" + uploadtime + "', '', '" + taskid + "', '" + fileInfo.toString() + "')";
            dao.execute(insertSql);
            dao.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 娣囶喖顦叉稉顓熸瀮閺傚洣娆㈤崥宥勮础閻線妫舵０锟�
     */
    private String fixChineseFileName(String fileName) {
        try {
            // 鐏忔繆鐦径姘鳖潚缂傛牜鐖滈弬鐟扮础娣囶喖顦叉稉顓熸瀮閺傚洣娆㈤崥锟�
            // 閺傝纭�1: ISO-8859-1 鏉烇拷 UTF-8
            String fixed = new String(fileName.getBytes("ISO-8859-1"), "UTF-8");
            if (isValidChineseString(fixed)) {
                return fixed;
            }

            // 閺傝纭�2: GBK 鏉烇拷 UTF-8
            fixed = new String(fileName.getBytes("GBK"), "UTF-8");
            if (isValidChineseString(fixed)) {
                return fixed;
            }

            // 閺傝纭�3: GB2312 鏉烇拷 UTF-8
            fixed = new String(fileName.getBytes("GB2312"), "UTF-8");
            if (isValidChineseString(fixed)) {
                return fixed;
            }

        } catch (UnsupportedEncodingException e) {
        }

        return fileName;
    }

    /**
     * 濡拷閺屻儱鐡х粭锔胯閺勵垰鎯侀崠鍛儓閺堝鏅ラ惃鍕厬閺傚洤鐡х粭锟�
     */
    private boolean isValidChineseString(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.matches(".*[\u4e00-\u9fa5].*") && !str.contains("?") && !str.contains("閿燂拷");
    }

    /**
     * 閸愭瑥鎼锋惔锟�
     */
    private void writeResponse(HttpServletResponse response, JSONObject result) throws IOException {
        PrintWriter out = response.getWriter();
        out.print(result.toString());
        out.flush();
        out.close();
    }

    /**
     * 瀵规枃浠跺悕杩涜URL缂栫爜
     */
    private String encodeFileName(String fileName) {
        try {
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            System.out.println("鏂囦欢鍚峌RL缂栫爜: " + fileName + " -> " + encodedFileName);
            return encodedFileName;
        } catch (UnsupportedEncodingException e) {
            System.out.println("鏂囦欢鍚峌RL缂栫爜澶辫触: " + e.getMessage());
            return fileName; // 缂栫爜澶辫触鏃朵娇鐢ㄥ師鏂囦欢鍚�
        }
    }
}
