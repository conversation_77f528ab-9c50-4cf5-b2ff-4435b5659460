package temp;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import dao.DaoCMCC;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.json.JSONObject;

import okhttp3.*;

/**
 * API涓婁紶Servlet
 * 浣跨敤OkHttp璋冪敤澶栭儴API鎺ュ彛锛岃В鍐冲墠绔疌ORS璺ㄥ煙闂
 */
@WebServlet("/ApiUploadServlet")
public class ApiUploadServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    // 涓婁紶鏂囦欢淇濆瓨鐩綍
    private static final String UPLOAD_DIRECTORY = "temp_uploads";
    // 涓婁紶閰嶇疆
    private static final int MEMORY_THRESHOLD = 1024 * 1024 * 3;  // 3MB
    private static final int MAX_FILE_SIZE = 1024 * 1024 * 2000; // 2000MB
    private static final int MAX_REQUEST_SIZE = 1024 * 1024 * 4000; // 4000MB
    
    // API閰嶇疆 - 鍐欐鍦ㄥ悗鍙�
    private static final String API_URL = "http://10.231.145.183:10888/api/report/upload/files";
    private static final long DEFAULT_TIMEOUT_SECONDS = 300; // 5鍒嗛挓
    
    // 濯掍綋绫诲瀷瀹氫箟
    private static final MediaType MEDIA_TYPE_OCTET = MediaType.get("application/octet-stream");

    private OkHttpClient httpClient;

    @Override
    public void init() throws ServletException {
        super.init();
        // 鍒濆鍖朞kHttp瀹㈡埛绔�
        httpClient = new OkHttpClient.Builder()
                .connectTimeout(DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .writeTimeout(DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .readTimeout(DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .build();
    }

    @Override
    public void destroy() {
        super.destroy();
        // 鍏抽棴OkHttp瀹㈡埛绔祫婧�
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
            httpClient.connectionPool().evictAll();
        }
    }

    public ApiUploadServlet() {
        super();
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        doPost(request, response);
    }

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 璁剧疆鍝嶅簲缂栫爜
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        request.setCharacterEncoding("UTF-8");

        JSONObject result = new JSONObject();
        
        // 妫�鏌ヨ姹傛槸鍚︿负multipart/form-data
        if (!ServletFileUpload.isMultipartContent(request)) {
            result.put("code", 1);
            result.put("msg", "涓婁紶澶辫触锛氳姹傛牸寮忛敊璇�");
            writeResponse(response, result);
            return;
        }

        try {
            // 閰嶇疆涓婁紶鍙傛暟
            DiskFileItemFactory factory = new DiskFileItemFactory();
            factory.setSizeThreshold(MEMORY_THRESHOLD);
            factory.setRepository(new File(System.getProperty("java.io.tmpdir")));

            ServletFileUpload upload = new ServletFileUpload(factory);
            upload.setFileSizeMax(MAX_FILE_SIZE);
            upload.setSizeMax(MAX_REQUEST_SIZE);
            upload.setHeaderEncoding("UTF-8"); // 璁剧疆澶撮儴缂栫爜

            // 瑙ｆ瀽璇锋眰鐨勫唴瀹规彁鍙栨枃浠舵暟鎹�
            List<FileItem> formItems = upload.parseRequest(request);

            String employeeId = "";
            String token = "";
            List<File> wngFiles = new ArrayList<>();
            List<File> audioFiles = new ArrayList<>();
            List<String> wngFileNames = new ArrayList<>();
            List<String> audioFileNames = new ArrayList<>();

            if (formItems != null && formItems.size() > 0) {
                // 鑾峰彇涓婁紶鐩綍鐨勭粷瀵硅矾寰�
                String uploadPath = getServletContext().getRealPath("") + File.separator + UPLOAD_DIRECTORY;
                File uploadDir = new File(uploadPath);
                if (!uploadDir.exists()) {
                    uploadDir.mkdir();
                }

                for (FileItem item : formItems) {
                    if (item.isFormField()) {
                        // 澶勭悊琛ㄥ崟瀛楁
                        String fieldName = item.getFieldName();
                        String fieldValue = item.getString("UTF-8");
                        
                        if ("employee_id".equals(fieldName)) {
                            employeeId = fieldValue;
                        } else if ("token".equals(fieldName)) {
                            token = fieldValue;
                        }
                    } else {
                        // 澶勭悊鏂囦欢瀛楁
                        String fieldName = item.getFieldName();
                        String fileName = item.getName();
                        
                        if (fileName != null && !fileName.isEmpty()) {
                            // 鐢熸垚涓存椂鏂囦欢鍚�
                            String tempFileName = generateFileName() + "_" + fileName;
                            String filePath = uploadPath + File.separator + tempFileName;
                            File tempFile = new File(filePath);

                            // 淇濆瓨鏂囦欢鍒颁复鏃剁洰褰�
                            item.write(tempFile);
                            
                            System.out.println("淇濆瓨涓存椂鏂囦欢: " + tempFileName + " 鍒拌矾寰�: " + filePath);

                            if ("wng_files".equals(fieldName)) {
                                wngFiles.add(tempFile);
                                wngFileNames.add(fileName);
                            } else if ("audio_files".equals(fieldName)) {
                                audioFiles.add(tempFile);
                                audioFileNames.add(fileName);
                            }
                        }
                    }
                }

                // 楠岃瘉蹇呰鍙傛暟
                if (employeeId.isEmpty() || token.isEmpty()) {
                    result.put("code", 1);
                    result.put("msg", "涓婁紶澶辫触锛氱己灏戝憳宸ュ伐鍙锋垨Token");
                    cleanupTempFiles(wngFiles);
                    cleanupTempFiles(audioFiles);
                    writeResponse(response, result);
                    return;
                }

                if (wngFiles.isEmpty() || audioFiles.isEmpty()) {
                    result.put("code", 1);
                    result.put("msg", "涓婁紶澶辫触锛氳鍚屾椂涓婁紶WNG鏂囦欢鍜孉udio鏂囦欢");
                    cleanupTempFiles(wngFiles);
                    cleanupTempFiles(audioFiles);
                    writeResponse(response, result);
                    return;
                }

                // 浣跨敤OkHttp璋冪敤API
                String apiResult = callUploadAPI(employeeId, token, wngFiles, audioFiles);
                
                // 娓呯悊涓存椂鏂囦欢
                cleanupTempFiles(wngFiles);
                cleanupTempFiles(audioFiles);
                
                // 瑙ｆ瀽API鍝嶅簲
                JSONObject apiResponse = new JSONObject(apiResult);
                
                // 妫�鏌PI鍝嶅簲
                if (apiResponse.has("task_id")) {
                    String taskId = String.valueOf(apiResponse.get("task_id"));

                    // 鎻掑叆鏁版嵁搴撹褰�
                    insertToAivonrlog2(taskId, employeeId, wngFileNames, audioFileNames);

                    result.put("code", 0);
                    result.put("msg", "涓婁紶鎴愬姛");
                    result.put("task_id", taskId);
                    result.put("data", apiResponse);
                } else {
                    result.put("code", 1);
                    result.put("msg", "API璋冪敤澶辫触锛�" + apiResult);
                }

            } else {
                result.put("code", 1);
                result.put("msg", "涓婁紶澶辫触锛氭病鏈夋枃浠�");
            }
        } catch (Exception ex) {
            result.put("code", 1);
            result.put("msg", "涓婁紶澶辫触锛�" + ex.getMessage());
            ex.printStackTrace();
        }

        writeResponse(response, result);
    }

    /**
     * 浣跨敤OkHttp璋冪敤涓婁紶API
     */
    private String callUploadAPI(String employeeId, String token, List<File> wngFiles, List<File> audioFiles) throws IOException {

        // 鏋勫缓multipart璇锋眰浣�
        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);

        // 娣诲姞琛ㄥ崟瀛楁
        builder.addFormDataPart("employee_id", employeeId);
        builder.addFormDataPart("token", token);

        // 娣诲姞WNG鏂囦欢
        for (File file : wngFiles) {
            RequestBody fileBody = RequestBody.create(file, MEDIA_TYPE_OCTET);
            builder.addFormDataPart("wng_files", file.getName(), fileBody);
            System.out.println("娣诲姞WNG鏂囦欢: " + file.getName());
        }

        // 娣诲姞Audio鏂囦欢
        for (File file : audioFiles) {
            RequestBody fileBody = RequestBody.create(file, guessAudioMediaType(file));
            builder.addFormDataPart("audio_files", file.getName(), fileBody);
            System.out.println("娣诲姞Audio鏂囦欢: " + file.getName());
        }

        RequestBody requestBody = builder.build();

        // 鏋勫缓璇锋眰
        Request request = new Request.Builder()
                .url(API_URL)
                .post(requestBody)
                .build();

        // 鍙戦�佽姹�
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("API璋冪敤澶辫触: HTTP " + response.code() + " - " + response.message());
            }

            ResponseBody responseBody = response.body();
            String responseText = responseBody != null ? responseBody.string() : "{}";

            System.out.println("API鍝嶅簲: " + responseText);
            return responseText;
        }
    }

    /**
     * 鐚滄祴闊抽鏂囦欢绫诲瀷
     */
    private MediaType guessAudioMediaType(File file) {
        String name = file.getName().toLowerCase();
        if (name.endsWith(".wav")) return MediaType.get("audio/wav");
        if (name.endsWith(".mp3")) return MediaType.get("audio/mpeg");
        if (name.endsWith(".m4a")) return MediaType.get("audio/mp4");
        if (name.endsWith(".aac")) return MediaType.get("audio/aac");
        if (name.endsWith(".flac")) return MediaType.get("audio/flac");
        if (name.endsWith(".ogg")) return MediaType.get("audio/ogg");
        if (name.endsWith(".amr")) return MediaType.get("audio/amr");
        return MEDIA_TYPE_OCTET;
    }

    /**
     * 娓呯悊涓存椂鏂囦欢
     */
    private void cleanupTempFiles(List<File> files) {
        for (File file : files) {
            try {
                if (file.exists() && file.delete()) {
                    System.out.println("鍒犻櫎涓存椂鏂囦欢: " + file.getAbsolutePath());
                }
            } catch (Exception e) {
                System.err.println("鍒犻櫎涓存椂鏂囦欢澶辫触: " + file.getAbsolutePath() + ", 閿欒: " + e.getMessage());
            }
        }
    }

    /**
     * 鐢熸垚闅忔満鏂囦欢鍚�
     */
    private String generateFileName() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = new Date();
        String str = simpleDateFormat.format(date);

        int rannum = (int)(Math.random() * 9000) + 1000;
        return str + "_" + rannum;
    }

    /**
     * 鎻掑叆鏁版嵁鍒癮ivonrlog2琛�
     */
    private void insertToAivonrlog2(String taskid, String userid, List<String> wngFileNames, List<String> audioFileNames) {
        try {
            Date d = new Date();
            SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String uploadtime = sbf.format(d);

            // 鏋勫缓鏂囦欢淇℃伅瀛楃涓�
            StringBuilder fileInfo = new StringBuilder();
            fileInfo.append("WNG鏂囦欢:");
            for (int i = 0; i < wngFileNames.size(); i++) {
                if (i > 0) fileInfo.append(", ");
                fileInfo.append(wngFileNames.get(i));
            }
            fileInfo.append("; Audio鏂囦欢:");
            for (int i = 0; i < audioFileNames.size(); i++) {
                if (i > 0) fileInfo.append(", ");
                fileInfo.append(audioFileNames.get(i));
            }

            DaoCMCC dao = new DaoCMCC();
            // 鎻掑叆鏁版嵁鍒癮ivonrlog2琛�
            String insertSql = "INSERT INTO aivonrlog2(userid, uploadtime, status, taskid, filename) VALUES('" + userid + "', '" + uploadtime + "', '', '" + taskid + "', '" + fileInfo.toString() + "')";
            dao.execute(insertSql);
            dao.close();

            System.out.println("鎻掑叆鏁版嵁搴撴垚鍔�: taskid=" + taskid + ", userid=" + userid);
        } catch (Exception e) {
            System.err.println("鎻掑叆鏁版嵁搴撳け璐�: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 鍐欏搷搴�
     */
    private void writeResponse(HttpServletResponse response, JSONObject result) throws IOException {
        PrintWriter out = response.getWriter();
        out.print(result.toString());
        out.flush();
        out.close();
    }
}
