package temp;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import dao.DaoCMCC;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.json.JSONObject;

import okhttp3.*;

/**
 * API上传Servlet
 * 使用OkHttp调用外部API接口，解决前端CORS跨域问题
 */
@WebServlet("/ApiUploadServlet")
public class ApiUploadServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    // 上传文件保存目录
    private static final String UPLOAD_DIRECTORY = "temp_uploads";
    // 上传配置
    private static final int MEMORY_THRESHOLD = 1024 * 1024 * 3;  // 3MB
    private static final int MAX_FILE_SIZE = 1024 * 1024 * 2000; // 2000MB
    private static final int MAX_REQUEST_SIZE = 1024 * 1024 * 4000; // 4000MB
    
    // API配置 - 写死在后台
    private static final String API_URL = "http://10.231.145.183:10888/api/report/upload/files";
    private static final long DEFAULT_TIMEOUT_SECONDS = 300; // 5分钟
    
    // 媒体类型定义
    private static final MediaType MEDIA_TYPE_OCTET = MediaType.get("application/octet-stream");

    private OkHttpClient httpClient;

    @Override
    public void init() throws ServletException {
        super.init();
        // 初始化OkHttp客户端
        httpClient = new OkHttpClient.Builder()
                .connectTimeout(DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .writeTimeout(DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .readTimeout(DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .build();
    }

    @Override
    public void destroy() {
        super.destroy();
        // 关闭OkHttp客户端资源
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
            httpClient.connectionPool().evictAll();
        }
    }

    public ApiUploadServlet() {
        super();
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        doPost(request, response);
    }

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 设置响应编码
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        request.setCharacterEncoding("UTF-8");

        JSONObject result = new JSONObject();
        
        // 检查请求是否为multipart/form-data
        if (!ServletFileUpload.isMultipartContent(request)) {
            result.put("code", 1);
            result.put("msg", "上传失败：请求格式错误");
            writeResponse(response, result);
            return;
        }

        try {
            // 配置上传参数
            DiskFileItemFactory factory = new DiskFileItemFactory();
            factory.setSizeThreshold(MEMORY_THRESHOLD);
            factory.setRepository(new File(System.getProperty("java.io.tmpdir")));

            ServletFileUpload upload = new ServletFileUpload(factory);
            upload.setFileSizeMax(MAX_FILE_SIZE);
            upload.setSizeMax(MAX_REQUEST_SIZE);
            upload.setHeaderEncoding("UTF-8"); // 设置头部编码

            // 解析请求的内容提取文件数据
            List<FileItem> formItems = upload.parseRequest(request);

            String employeeId = "";
            String token = "";
            List<File> wngFiles = new ArrayList<>();
            List<File> audioFiles = new ArrayList<>();
            List<String> wngFileNames = new ArrayList<>();
            List<String> audioFileNames = new ArrayList<>();

            if (formItems != null && formItems.size() > 0) {
                // 获取上传目录的绝对路径
                String uploadPath = getServletContext().getRealPath("") + File.separator + UPLOAD_DIRECTORY;
                File uploadDir = new File(uploadPath);
                if (!uploadDir.exists()) {
                    uploadDir.mkdir();
                }

                for (FileItem item : formItems) {
                    if (item.isFormField()) {
                        // 处理表单字段
                        String fieldName = item.getFieldName();
                        String fieldValue = item.getString("UTF-8");
                        
                        if ("employee_id".equals(fieldName)) {
                            employeeId = fieldValue;
                        } else if ("token".equals(fieldName)) {
                            token = fieldValue;
                        }
                    } else {
                        // 处理文件字段
                        String fieldName = item.getFieldName();
                        String fileName = item.getName();
                        
                        if (fileName != null && !fileName.isEmpty()) {
                            // 生成临时文件名
                            String tempFileName = generateFileName() + "_" + fileName;
                            String filePath = uploadPath + File.separator + tempFileName;
                            File tempFile = new File(filePath);

                            // 保存文件到临时目录
                            item.write(tempFile);
                            
                            System.out.println("保存临时文件: " + tempFileName + " 到路径: " + filePath);

                            if ("wng_files".equals(fieldName)) {
                                wngFiles.add(tempFile);
                                wngFileNames.add(fileName);
                            } else if ("audio_files".equals(fieldName)) {
                                audioFiles.add(tempFile);
                                audioFileNames.add(fileName);
                            }
                        }
                    }
                }

                // 验证必要参数
                if (employeeId.isEmpty() || token.isEmpty()) {
                    result.put("code", 1);
                    result.put("msg", "上传失败：缺少员工工号或Token");
                    cleanupTempFiles(wngFiles);
                    cleanupTempFiles(audioFiles);
                    writeResponse(response, result);
                    return;
                }

                if (wngFiles.isEmpty() || audioFiles.isEmpty()) {
                    result.put("code", 1);
                    result.put("msg", "上传失败：请同时上传WNG文件和Audio文件");
                    cleanupTempFiles(wngFiles);
                    cleanupTempFiles(audioFiles);
                    writeResponse(response, result);
                    return;
                }

                // 使用OkHttp调用API
                String apiResult = callUploadAPI(employeeId, token, wngFiles, audioFiles);
                
                // 清理临时文件
                cleanupTempFiles(wngFiles);
                cleanupTempFiles(audioFiles);
                
                // 解析API响应
                JSONObject apiResponse = new JSONObject(apiResult);
                
                // 检查API响应
                if (apiResponse.has("task_id")) {
                    String taskId = String.valueOf(apiResponse.get("task_id"));

                    // 插入数据库记录
                    insertToAivonrlog2(taskId, employeeId, wngFileNames, audioFileNames);

                    result.put("code", 0);
                    result.put("msg", "上传成功");
                    result.put("task_id", taskId);
                    result.put("data", apiResponse);
                } else {
                    result.put("code", 1);
                    result.put("msg", "API调用失败：" + apiResult);
                }

            } else {
                result.put("code", 1);
                result.put("msg", "上传失败：没有文件");
            }
        } catch (Exception ex) {
            result.put("code", 1);
            result.put("msg", "上传失败：" + ex.getMessage());
            ex.printStackTrace();
        }

        writeResponse(response, result);
    }

    /**
     * 使用OkHttp调用上传API
     */
    private String callUploadAPI(String employeeId, String token, List<File> wngFiles, List<File> audioFiles) throws IOException {

        // 构建multipart请求体
        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);

        // 添加表单字段
        builder.addFormDataPart("employee_id", employeeId);
        builder.addFormDataPart("token", token);

        // 添加WNG文件
        for (File file : wngFiles) {
            RequestBody fileBody = RequestBody.create(file, MEDIA_TYPE_OCTET);
            builder.addFormDataPart("wng_files", file.getName(), fileBody);
            System.out.println("添加WNG文件: " + file.getName());
        }

        // 添加Audio文件
        for (File file : audioFiles) {
            RequestBody fileBody = RequestBody.create(file, guessAudioMediaType(file));
            builder.addFormDataPart("audio_files", file.getName(), fileBody);
            System.out.println("添加Audio文件: " + file.getName());
        }

        RequestBody requestBody = builder.build();

        // 构建请求
        Request request = new Request.Builder()
                .url(API_URL)
                .post(requestBody)
                .build();

        // 发送请求
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("API调用失败: HTTP " + response.code() + " - " + response.message());
            }

            ResponseBody responseBody = response.body();
            String responseText = responseBody != null ? responseBody.string() : "{}";

            System.out.println("API响应: " + responseText);
            return responseText;
        }
    }

    /**
     * 猜测音频文件类型
     */
    private MediaType guessAudioMediaType(File file) {
        String name = file.getName().toLowerCase();
        if (name.endsWith(".wav")) return MediaType.get("audio/wav");
        if (name.endsWith(".mp3")) return MediaType.get("audio/mpeg");
        if (name.endsWith(".m4a")) return MediaType.get("audio/mp4");
        if (name.endsWith(".aac")) return MediaType.get("audio/aac");
        if (name.endsWith(".flac")) return MediaType.get("audio/flac");
        if (name.endsWith(".ogg")) return MediaType.get("audio/ogg");
        if (name.endsWith(".amr")) return MediaType.get("audio/amr");
        return MEDIA_TYPE_OCTET;
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(List<File> files) {
        for (File file : files) {
            try {
                if (file.exists() && file.delete()) {
                    System.out.println("删除临时文件: " + file.getAbsolutePath());
                }
            } catch (Exception e) {
                System.err.println("删除临时文件失败: " + file.getAbsolutePath() + ", 错误: " + e.getMessage());
            }
        }
    }

    /**
     * 生成随机文件名
     */
    private String generateFileName() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = new Date();
        String str = simpleDateFormat.format(date);

        int rannum = (int)(Math.random() * 9000) + 1000;
        return str + "_" + rannum;
    }

    /**
     * 插入数据到aivonrlog2表
     */
    private void insertToAivonrlog2(String taskid, String userid, List<String> wngFileNames, List<String> audioFileNames) {
        try {
            Date d = new Date();
            SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String uploadtime = sbf.format(d);

            // 构建文件信息字符串
            StringBuilder fileInfo = new StringBuilder();
            fileInfo.append("WNG文件:");
            for (int i = 0; i < wngFileNames.size(); i++) {
                if (i > 0) fileInfo.append(", ");
                fileInfo.append(wngFileNames.get(i));
            }
            fileInfo.append("; Audio文件:");
            for (int i = 0; i < audioFileNames.size(); i++) {
                if (i > 0) fileInfo.append(", ");
                fileInfo.append(audioFileNames.get(i));
            }

            DaoCMCC dao = new DaoCMCC();
            // 插入数据到aivonrlog2表
            String insertSql = "INSERT INTO aivonrlog2(userid, uploadtime, status, taskid, filename) VALUES('" + userid + "', '" + uploadtime + "', '', '" + taskid + "', '" + fileInfo.toString() + "')";
            dao.execute(insertSql);
            dao.close();

            System.out.println("插入数据库成功: taskid=" + taskid + ", userid=" + userid);
        } catch (Exception e) {
            System.err.println("插入数据库失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 写响应
     */
    private void writeResponse(HttpServletResponse response, JSONObject result) throws IOException {
        PrintWriter out = response.getWriter();
        out.print(result.toString());
        out.flush();
        out.close();
    }
}
