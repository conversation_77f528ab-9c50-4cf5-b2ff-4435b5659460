/**
 * 
 */
var myjson;

layui.use(function(){
  var laypage=layui.laypage;
  var layer = layui.layer;
  var table = layui.table;
  // 渲染
  table.render({
	type:'post',
    elem: '#table0',
    url:'QueryVonrTableServlet', // 此处为静态模拟数据，实际使用时需换成真实接口
    even: false,
    toolbar: '#page0',
    id:'testReload0',
    request: {
          pageName: 'pageIndex',  // 页码的参数名称，默认：page
          limitName: 'pageSize'   // 每页数据量的参数名，默认：limit
	
	  },
	response: {
          statusName: 'code',     // 规定数据状态的字段名称，默认：code
          statusCode: 0,          // 规定成功的状态码，默认：0
          countName: 'count', // 规定数据总数的字段名称，默认：count
          msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
          dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
	  },
    cols: [[
        {field:'dataid', title:'任务ID', width:'6%'},
        {field:'rowId', title:'子任务ID', width:'7%'},
        {field:'status', title:'状态',width:'10%',templet: function(d) {
																            if (d.status=="success") {
																              return '<span style="color: green;">' + d.status+ '</span>';
																            }
																            else if((d.status=="fail")){
																	 return '<span style="color: red;">' + d.status + '</span>';
																}}},
        {field:'start', title:'开始时间',width:'15%'},
        {field:'end', title:'结束时间',width:'15%'},
        {field:'filename', title:'文件名',width:'20%'},
        {field:'result', title:'结果',width:'10%'},
        {field:'lossInfo', title:'详情',width:'10%'},
        {field:'operate', title:'操作',width:'10%',toolbar:'#bardown1'},
//		 ,{field:'other', title: '操作',align: 'center',width:'30%',toolbar:'#bardown1'}

      ]],
      done: function(res, curr, count){
	     var that = $("#table0").siblings();

     res.data.forEach(function (item, index) {
		console.log(item);
         if (item.rowId=="total") {
            var tr = that.find(".layui-table-box tbody tr[data-index='" + index + "']");
            tr.css("background-color", "#F8F8FF");
         }
//         else{
//			var tr = that.find(".layui-table-box tbody tr[data-index='" + index + "']");
//            tr.css("background-color", "");
//			}
    });
},
    
   page: {layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
	    curr: 1,      // 设置默认起始页1
	    groups: 5,   //只显示10个连续页码,就是说显示10个可见页其他的省略
	    first: false, // 不显示首页
	    last: false   // 不显示尾页
		},
   text:{
	    none:'暂无相关数据'},
	     limit: 20,
		limits: [5,10,15,20,100,1000]
  });


  
  		table.on('toolbar(lay0)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            switch(obj.event){
                case 'search': 
                    search();
                    break;
            }
        });

      table.on( 'tool(lay0)',function (obj) {
			console.log(obj);
			switch(obj.event){
			        case 'start1':
			        	start1(obj.data.dataid,obj.data.rowId);
		            break;
		
		    }
		}); 

 function search(){
				 	var demoReload = document.getElementById("tag0").value;
//	                console.log(demoReload);
	                table.reload('testReload0', {
                    url:"QueryVonrTableSearchServlet",
                    request: {
                              pageName: 'pageIndex',  // 页码的参数名称，默认：page
                              limitName: 'pageSize', // 每页数据量的参数名，默认：limit
//								search_data:'id'
                      },
                    response: {
                             statusName: 'code',     // 规定数据状态的字段名称，默认：code
                             statusCode: 0,          // 规定成功的状态码，默认：0
                             countName: 'count', // 规定数据总数的字段名称，默认：count
                             msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                             dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
                      },
                    page: {
                            layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                            curr: 1,      // 设置默认起始页1
                            groups: 10,   //只显示10个连续页码,就是说显示10个可见页其他的省略
                            first: false, // 不显示首页
                            last: false   // 不显示尾页
                        },
                        limit: 20,
                        limits: [5,10,15,20,100,1000]

                    ,where: {
                        search_data: encodeURI(demoReload),
                    }
                  }, 'data');
                  
             document.getElementById("tag0").value=demoReload;
             }
             
             
      function start1(dataid,rowid){
			var url = "https://iask.zte.com.cn/api/whisper/audio/"+dataid+"/"+rowid+"";
//			window.open(url);
		    layer.open({
		      type: 2,
		      title:false,
		      area: ['30%', '30%'],	
		      
		      content: url
		//      success: function(layero, index){
		//            layer.full(index);
		//          }
		    });
		}

});
 
  	function tap1() {
				var index = layer.open({
					type: 1, // page 层类型，其他类型详见「基础属性」
					 content: "http://210.74.157.104:9200/iWork2Agent/test.jsp",
					 area:["500px","100px"]
					});
    }
    
      function tap2() {
		window.open("http://10.226.215.4:8080/iWork2Agent/test.jsp");
    }
    
      function tap3() {
		window.open("http://10.232.104.50:8080/iWork2Agent/test.jsp");
    }
                      
         
                                                     