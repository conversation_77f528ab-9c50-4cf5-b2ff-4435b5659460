<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>5G性能守护环境</title>
    <link rel="stylesheet" type="text/css" href="res/css/monitor.css"/>
    <script src="res/js/highcharts.js" charset="utf-8"></script>
</head>
<body>
	<span id="bugBtn" title="同步过程出现错误数据，请检查" onclick="hideBugBtn('')"></span>
	<span id="localtime" style=" font-size:14px; position: absolute; z-index: 10; right: 25px; top:5px; "></span>
	<span class="btn btn-blue btn-big" id="refreshBtn" title="默认 1min 刷新一次">刷新</span>
	<div style="height: 600px;">
		<header class="t_header"><span>5G性能守护环境</span></header>
		<div style="margin-top: 20px;">
			<div class="t_left_box">
				<img class="t_l_line" src="res/images/left_line.png" alt="">
				<div class="t_mbox">
					<div>总站点数量</div>	<span id="devnum">0</span> <i></i>
				</div>
				<div class="t_mbox">
					<div>室内站点数</div>	<span id="devnumofindoor">0</span> <i></i>
				</div>
				<div class="t_mbox">
					<div>室外站点数</div>	<span id="devnumofoutdoor">0</span><i></i>
				</div>
				<div class="t_mbox">
					<div>最近更新</div>		<span id="updatetime">2022-01-18</span>
				</div>
				<img class="t_r_line" src="res/images/right_line.png" alt="">
			</div>
			<div class="t_top_box">
				<img class="t_l_line" src="res/images/left_line.png" alt="">
				<ul class="t_nav">
					<li><span>主控板类型</span> <span id="vsw">0</span> <i></i></li>
					<li><span>基带板类型</span> <span id="vbp">0</span> <i></i></li>
					<li><span>AAU/RRU类型</span> <span id="aau">0</span></li>
				</ul>
				<img class="t_r_line" src="res/images/right_line.png" alt="">
			</div>
			<div class="t_top2_box">
				<img class="t_l_line" src="res/images/left_line.png" alt="">
				<div class="t_mbox2">
					<span style="color: #00b3ac; float: left; margin-left: 13%;">小区状态：</span>
					<span id="" style="float: left; margin-left: 5%;"><a  href="cell.jsp" target="_blank">NR小区监控</a></span>
				</div>
				<img class="t_r_line" src="res/images/right_line.png" alt="">
			</div>
			<div class="t_bottom_box">
				<img class="t_l_line" src="res/images/left_line.png" alt="">
				<div class="chart" id="chart" style="width: 100%; height: 100%;"></div>
				<img class="t_r_line" src="res/images/right_line.png" alt="">
			</div>
		</div>
	</div>
	<div class="t_table_box">
		<img class="t_l_line" src="res/images/left_line.png" alt=""> 
		<input	type="text" id="searchText" value="" placeholder="输入内容筛选" />
		<table class="commonTable" >
			<thead><td title="序号" colspan="8">no monitor data</td></thead>
		</table>
		<img class="t_r_line" src="res/images/right_line.png" alt="">
	</div>
<script src="res/js/jquery.min.js"></script>

<script type="text/javascript" src="res/js/jquery.count.min.js"></script>
<script type="text/javascript" src="res/js/monitor.js"></script>
<script type="text/javascript">init();</script>	
</body>
</html>