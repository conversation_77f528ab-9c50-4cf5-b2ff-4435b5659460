package servlet;

import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Timer;
import java.util.TimerTask;


import javax.mail.Address;
import javax.mail.Message;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;

import dao.DaoCMCC;



/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryVonrInfoServlet2")
public class QueryVonrInfoServlet2 extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;   
	 private MyThread2 myThread; 
	 private HashMap<String,String> map =  new HashMap<>();
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryVonrInfoServlet2() {
        super();
        // TODO Auto-generated constructor stub
    }
    
    
	@Override
	public void init() throws ServletException {
		// TODO Auto-generated method stub
		//super.init();
		 System.out.println("开始");
//		 this.timerTask();
	}


	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
//    	Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//	    String updatetime=sbf.format(d);
//	    
//	    LocalDateTime localTime = LocalDateTime.parse(updatetime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//		LocalDateTime newTime = localTime.plus(-24, ChronoUnit.HOURS);
//		String starttime = newTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
		QueryVonrInfo("568");
		
	}
	
	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}

	

	
	//GET锟斤拷锟斤拷, 锟斤拷询锟斤拷元锟斤拷锟斤拷状态
	protected void QueryVonrInfo(String dataid) {		
			try {
				

				String url = "http://10.231.145.183:18209/api/whisper/inquire/"+dataid+"";
				System.out.println(url);
		        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
		        		.header("content-type", "application/json")
		        	    .header("accept", "application/json")
		                .asJson(); 
//		        System.out.println(httpresponse.getBody().toString());
			     JSONObject resultObject=httpresponse.getBody().getObject();
//			     System.out.println(resultObject);
			     if(!resultObject.isNull("result")) {
			    	 String resultString = resultObject.getString("result");
			    	 System.out.println(resultString);

//			    	 System.out.println(resultString);
			    	 JSONObject object = new JSONObject(resultString);
		    		 String filename = "";
		    		 String status = "";
					if(!object.isNull("fileName")) {
						filename = object.getString("fileName") ;
					}
					if(!resultObject.isNull("status")) {
						status = resultObject.getString("status") ;
					}
					System.out.println(status);
					System.out.println(filename);
			    	 if(!object.isNull("result_rows")) {
			    		 JSONArray array = object.getJSONArray("result_rows");
			    		 System.out.println(array);
			    		 if(array.length()>0) {

						      String updateSql="insert into aivonrrecord2(dataid,rowId,status,start,end,updatetime,filename,result,lossInfo) values";

						      Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							    String updatetime=sbf.format(d);
							    for(int i=0;i<array.length();i++) {
						    		 JSONObject tempJsonObject = array.getJSONObject(i);
						    		 String rowId = "";
						    		 String start = "";
						    		 String end = "";
						    		 String result = "";
						    		 String lossInfo = "";
						    		 if(!tempJsonObject.isNull("rowId")) {
						    			 rowId = String.valueOf(tempJsonObject.getInt("rowId")) ;
						    		 }
									if(!tempJsonObject.isNull("start")) {
										start = 	  tempJsonObject.getString("start");  			 
												    		 }
									if(!tempJsonObject.isNull("end")) {
										end = 	  tempJsonObject.getString("end");  			 
		
									}
									if(!tempJsonObject.isNull("lossInfo")) {
										lossInfo = 	  tempJsonObject.getString("lossInfo");  			 
	
									}
									if(!tempJsonObject.isNull("result")) {
										result = 	  tempJsonObject.getString("result");  			 
	
									}
									
									if(i==array.length()-1)
										updateSql+= "('"+dataid+"','"+rowId+"','"+status+"','"+start+"','"+end+"','"+updatetime+"','"+filename+"','"+result+"','"+lossInfo+"')";
									else
										updateSql+= "('"+dataid+"','"+rowId+"','"+status+"','"+start+"','"+end+"','"+updatetime+"','"+filename+"','"+result+"','"+lossInfo+"'),";
		
						    	 	}
							    
							    
							     updateSql+=" on duplicate key update dataid=values(dataid),rowId=values(rowId),status=values(status),start=values(start),end=values(end)"
					     		+ ",updatetime=values(updatetime),filename=values(filename),result=values(result),lossInfo=values(lossInfo);";
							     DaoCMCC dao=new DaoCMCC();
							     dao.execute(updateSql);
								 dao.close();
								System.out.println(updateSql);
							    }
			    		 }
			    	 }
			     

		    } catch (Exception e) {
		    	e.printStackTrace();
		    }
		}

	
	public  void timerTask() {  
        new Timer().schedule(new TimerTask(){  
            @Override  
            public void run() {  
            	myThread = new MyThread2();  
	            myThread.start();	
            }},new Date(),1000*3);  
  } 
	
	  class MyThread2 extends Thread { 
		    public void run() {
//		    	Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//			    String updatetime=sbf.format(d);
//			    
//			    LocalDateTime localTime = LocalDateTime.parse(updatetime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//				LocalDateTime newTime = localTime.plus(-24, ChronoUnit.HOURS);
//				String starttime = newTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//				System.out.println(starttime);
//				System.out.println(updatetime);
				
				QueryVonrInfo("568");
		    	
		    }	      
		}
}




