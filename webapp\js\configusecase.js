var xmlHttp=false;  
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}
var tempid;
function init(id){
	tempid = id;
	createXMLHttpRequest();
	xmlHttp.onreadystatechange = initcallBack;
	var url="QueryConfigUsecaseInfoServlet?id="+id;  
	xmlHttp.open("GET", url, true);
	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
	xmlHttp.send(null);	
}

function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			table(data[0]);
			table1(data[1]);
			table2(data[2]);
			document.getElementById("chooseeduemodel").innerText=data[3]; 
			if(data[3]=="自定义业务模型(数量)"){
					document.getElementById("uemodelinfo").style.display='block';
					document.getElementById("uemodelinfo2").style.display='none';

			}
			if(data[3]=="自定义业务模型(固定终端)"){
					document.getElementById("uemodelinfo").style.display='none';
					document.getElementById("uemodelinfo2").style.display='block';
			}
			if(data[4]==null || data[4].length==0){
				
			}else if(data[3]=="自定义业务模型(数量)"){
				uemodelinfo(data[4]);
			}else if(data[3]=="自定义业务模型(固定终端)"){
				uemodelinfo2(data[4]);
			}

		}
	}	
}

function uemodelinfo(data){
	document.getElementById("uemodel1").innerText=data[0];
	document.getElementById("uemodel2").innerText=data[1]; 
	document.getElementById("uemodel3").innerText=data[2]; 
	document.getElementById("uemodel4").innerText=data[3]; 
	document.getElementById("uemodel5").innerText=data[4]; 
	document.getElementById("uemodel6").innerText=data[5]; 
}

function uemodelinfo2(data){
	
	if(data[0]==null) 	document.getElementById("uemodel2_1").innerText=""; 
	else document.getElementById("uemodel2_1").innerText=data[0];
	
	if(data[1]==null) 	document.getElementById("uemodel2_2").innerText=""; 
	else 	document.getElementById("uemodel2_2").innerText=data[1]; 

	if(data[2]==null) 	document.getElementById("uemodel2_3").innerText=""; 
	else document.getElementById("uemodel2_3").innerText=data[2]; 
	
	if(data[3]==null) 	document.getElementById("uemodel2_4").innerText=""; 
	else document.getElementById("uemodel2_4").innerText=data[3]; 
	
	if(data[4]==null) 	document.getElementById("uemodel2_5").innerText=""; 
	else document.getElementById("uemodel2_5").innerText=data[4]; 
	
	if(data[5]==null) 	document.getElementById("uemodel2_6").innerText=""; 
	else document.getElementById("uemodel2_6").innerText=data[5]; 
	
	if(data[6]==null) 	document.getElementById("uemodel2_7").innerText=""; 
	else document.getElementById("uemodel2_7").innerText=data[6]; 
	
	if(data[7]==null) 	document.getElementById("uemodel2_8").innerText=""; 	
	else document.getElementById("uemodel2_8").innerText=data[7]; 

	if(data[8]==null) 	document.getElementById("uemodel2_9").innerText=""; 
	else document.getElementById("uemodel2_9").innerText=data[8]; 
	
	if(data[9]==null) 	document.getElementById("uemodel2_10").innerText=""; 
	else document.getElementById("uemodel2_10").innerText=data[9]; 
}

 function check(){
	var s=document.uploadform.y.value;
	if(confirm("确认是否上传")){
		if(s==""){
			alert("未选择文件，请选择文件!");
			return false;
		}
				return init(tempid);
	}else{
		return false;
}}

function table(data){
	var tbody  = document.getElementById("tbody") ; 
	s="";
	for(var i=0;i<data.length;i++){
		s+="<td><input type='tel' name='phone' lay-verify='required' autocomplete='off' class='layui-input functionname' value='"+data[i].functionname+"'></td>";
		s+="<td><input type='tel' name='phone' lay-verify='required' autocomplete='off' class='layui-input mocname' value='"+data[i].mocname+"'></td>";
		s+="<td><input type='tel' name='phone' lay-verify='required' autocomplete='off' class='layui-input paraname' value='"+data[i].paraname+"' ></td>";
		s+="<td><input type='tel' name='phone' lay-verify='required' autocomplete='off' class='layui-input defaultvalue' value='"+data[i].defaultvalue+"'></td>";
		s+="<td><input type='tel' name='phone' lay-verify='required' autocomplete='off' class='layui-input suggestvalue' value='"+data[i].suggestvalue+"'></td>";
		s+="<td>"+"<a class='layui-btn layui-btn-xs' onclick='delCustomList(this)'>删除</a>"+"</td>";
		s+="</tr>"
	}
	tbody.innerHTML=s;
}

function table1(data){
	var tbody  = document.getElementById("tbody2") ; 
	s="";
	if(data.length==0){
	}else{
		for(var i=0;i<data.length;i++){
			if(data[i].nameofb==null ||data[i].nameofb==""){
				s+="<td></td>";
				s+="<td>"+"<a class='layui-btn layui-btn-xs' onclick='delparam(1)'>删除</a>"+"</td>";
				s+="<td>"+"<a class='layui-btn layui-btn-xs' onclick='downparamB()'>下载</a>"+"</td>";
				s+="</tr>"
			}
			else{
				s+="<td>"+data[i].nameofb+"</td>";
				s+="<td>"+"<a class='layui-btn layui-btn-xs' onclick='delparam(1)'>删除</a>"+"</td>";
				s+="<td>"+"<a class='layui-btn layui-btn-xs' onclick='downparamB()'>下载</a>"+"</td>";
				s+="</tr>"
			}
				
		}
	}
	tbody.innerHTML=s;
}

function table2(data){
	var tbody  = document.getElementById("tbody3") ; 
	s="";
	if(data.length==0){
	}else{
		for(var i=0;i<data.length;i++){
			if(data[i].nameofb==null ||data[i].nameofb==""){
				s+="<td></td>";
				s+="<td></td>";
				s+="<td>"+"<a class='layui-btn layui-btn-xs' onclick='delparam(2)'>删除</a>"+"</td>";
				s+="<td>"+"<a class='layui-btn layui-btn-xs' onclick='downparam()'>下载</a>"+"</td>";
			}
				
			else{
				s+="<td>"+data[i].nameofb+"</td>";
				if(data[i].remark=='2_1'){
					s+="<td>"+"参数套一"+"</td>";
				}else{
					s+="<td>"+"参数套二"+"</td>";
				}
				s+="<td>"+"<a class='layui-btn layui-btn-xs' onclick='delparam(2)'>删除</a>"+"</td>";
				s+="<td>"+"<a class='layui-btn layui-btn-xs' onclick='downparam()'>下载</a>"+"</td>";
			}
				

			s+="</tr>"
		}
	}
	tbody.innerHTML=s;
}

  function saveCustomList(id){
    var mytable = document.getElementById(id);
    var data = [];
    for(var i=0,rows=mytable.rows.length; i<rows-1; i++){
        if(!data[i]){
          data[i] = new Array();
        }
        data[i][0] = document.getElementsByClassName('functionname')[i].value;
        data[i][1] = document.getElementsByClassName('mocname')[i].value;
        data[i][2] = document.getElementsByClassName('paraname')[i].value;
        data[i][3] = document.getElementsByClassName('defaultvalue')[i].value;
        data[i][4] = document.getElementsByClassName('suggestvalue')[i].value;
    }
    return data;
  }  
    
    
   function SubmitTableContent(div,id){
	    var data = saveCustomList(div);	    
	    var tmp = '';
	    var flag=0;
	    for(i=0,rows=data.length; i<rows; i++){
	      for(j=0,cells=data[i].length; j<cells; j++){
			if(j==cells-1){
				if(data[i][j]==null||data[i][j]==""){
					flag=1;
					break;
				}else{
					tmp += data[i][j].trim();
				}
			}else{
				if(data[i][j]==null||data[i][j]==""){
					flag=1;
					break;
				}else{
					tmp += data[i][j].trim() + '#';

				}
			}
	        
	      }
	      tmp += '<br>';
	    }
	    if(flag==1){
			alert("部分参数为空，请核查")
		}else{
			createXMLHttpRequest();
			xmlHttp.onreadystatechange = submitcallBack;
			var url="SaveConfigUsecaseInfoServlet";
	    	xmlHttp.open("POST", url, true);
	    	xmlHttp.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
			data="data@"+tmp+"&id@"+id;
	    	xmlHttp.send(data);
		}
    	
  }
  
  
function submitcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			layui.use(['layer'], function(){      
				layer = layui.layer;//弹出层		
				layer.msg('已保存'); 
			});
			
			init(tempid)
		}
	}	
}


function  chooseuemodel(){
	var uemodel=null; var s  =  document.getElementById( "uemodel" ); 
	uemodel=s.options[s.selectedIndex].value;
	if(uemodel=='00'){
		alert("请选择")	
	}else if(uemodel=='8'){
		document.getElementById("uemodelinfo").style.display='block';
		document.getElementById("uemodelinfo2").style.display='none';
	}else if(uemodel=='9'){
		document.getElementById("uemodelinfo2").style.display='block';
		document.getElementById("uemodelinfo").style.display='none';
	}else{
		document.getElementById("uemodelinfo").style.display='none';
		document.getElementById("uemodelinfo2").style.display='none';
	}
	document.getElementById("chooseeduemodel").innerText=s.options[s.selectedIndex].text; 
		createXMLHttpRequest();
	    xmlHttp.onreadystatechange = chooseuemodelcallBack;
	    var url="QueryUsecaseInfoChangeUemodelServlet?uemodel="+uemodel+"&id="+tempid; 
	    
	    xmlHttp.open("POST", url, true);
	    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
	    xmlHttp.send(null);	

}

function chooseuemodelcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
		}
     }
}

function delparam(remark){
	var r = confirm("您确认要删除嘛");
	if(r==true){
		var td = event.srcElement;
		tr = td.parentElement.parentElement;
		var name=tr.cells[0].innerText;
		name=encodeURIComponent(name);
    	name=encodeURIComponent(name);
    	console.log(name);
		createXMLHttpRequest();
		xmlHttp.onreadystatechange = delparamcallBack;
		var url="FileDeleteParamServlet?id="+tempid+"&remark="+remark+"&name="+name;  
		xmlHttp.open("GET", url, true);
		xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
		xmlHttp.send(null);
	}
}



function delparamcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
            init(tempid);
		}
     }
}

function downparam(){
	var r = confirm("您确认要下载嘛");
	if(r==true){
		var td = event.srcElement;
		tr = td.parentElement.parentElement;
		var name=tr.cells[0].innerText;
		var remark=tr.cells[1].innerText;
//    	name=encodeURIComponent(name);
		console.log(name);
		console.log(remark);
		window.location.href="/iWork2UseCase/downloadparamlist?id="+tempid+"&remark="+remark+"&name="+name;
	}
}

function downparamB(){
	var r = confirm("您确认要下载嘛");
	if(r==true){
		var td = event.srcElement;
		tr = td.parentElement.parentElement;
		var name=tr.cells[0].innerText;
		window.location.href="/iWork2UseCase/downloadparamB?id="+tempid+"&name="+name;
	}
}

function okuemodel(){
    var table = document.getElementById("uemodelinfos");
    var data = [];
 	for (let i = 1; i < table.rows.length; i++) {
  		let row = table.rows[i];  
  		for (let j = 1; j < row.cells.length-1; j++) {
    		let cell = row.cells[j];  
    		let cellContent = cell.innerText;  
    		 data.push(cellContent);
		}
	}
	console.log(data);
	createXMLHttpRequest();
	xmlHttp.onreadystatechange = okuemodelcallBack;
	var url="SaveUemodelServlet?id="+tempid+"&data="+data;  
	xmlHttp.open("GET", url, true);
	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
	xmlHttp.send(null);
}

function okuemodelcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            layui.use(['layer'], function(){      
				layer = layui.layer;//弹出层		
				layer.msg('已保存'); 
			});
		}
     }
}

function okuemodel2(){
    var table = document.getElementById("uemodelinfo2s");
    var data = "";
 	for (let i = 1; i < table.rows.length; i++) {
  		let row = table.rows[i];  
  		for (let j = 1; j < row.cells.length-1; j++) {
    		let cell = row.cells[j];  
    		let cellContent = cell.innerText;  
//    		 data.push(cellContent);
			data = data + cellContent +"/";
		}
	}
	console.log(data);
	data = data.substring(0,data.length-1);
	createXMLHttpRequest();
	xmlHttp.onreadystatechange = okuemodelcallBack2;
	var url="SaveUemodelServlet?id="+tempid+"&data="+data;  
	xmlHttp.open("GET", url, true);
	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
	xmlHttp.send(null);
}

function okuemodelcallBack2()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            layui.use(['layer'], function(){      
				layer = layui.layer;//弹出层		
				layer.msg('已保存'); 
			});
		}
     }
}

function downdemofilelog(){
	window.location.href='/iWork2Use/downloadfile'
}

