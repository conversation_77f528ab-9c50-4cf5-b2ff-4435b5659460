function sleep(ms){
	let start = Date.now();
	let end = start + ms;
	while(true){
		if(Date.now()> end){
			return
		}
	}
}

var xmlHttp = false;
function createXMLHttpRequest() {
	if (window.ActiveXObject) {
		xmlHttp = new ActiveXObject("Microsoft.XMLHTTP");
	}
	else if (window.XMLHttpRequest) {
		xmlHttp = new XMLHttpRequest();
	}
}

function pciopen(){
	var gnbid  =  document.getElementById( "gnbid" ).value; 
	var pci  =  document.getElementById( "pci" ).value; 
	var ldn  =  document.getElementById( "ldn" ).value; 
	ldn=encodeURIComponent(ldn);
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = pciopencallBack; 
    var url=encodeURI("GnbidupdateServlet?gnbid="+gnbid+"&pci="+pci+"&ldn="+ldn+"&flag="+"1");

    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}

function pciopencallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);	
			alert("已操作，请查看");
		}
	}
}

function pciclose(){
	var gnbid  =  document.getElementById( "gnbid" ).value; 
	var pci  =  document.getElementById( "pci" ).value; 
	var ldn  =  document.getElementById( "ldn" ).value; 
	ldn=encodeURIComponent(ldn);
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = pciclosecallBack; 
    var url=encodeURI("GnbidupdateServlet?gnbid="+gnbid+"&pci="+pci+"&ldn="+ldn+"&flag="+"0");

    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}

function pciclosecallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);	
			alert("已操作，请查看");
		}
	}
}