var myjson;
var subTableId; // 定义全局变量
var logininfo = islogininfo(); // 获取登录信息

layui.use(function () {
    var laypage = layui.laypage;
    var layer = layui.layer;
    var table = layui.table;
    var upload = layui.upload;
    var element = layui.element;

    // 渲染主表格
    function renderMainTable() {
        table.render({
            type: 'post',
            elem: '#table0',
            url: 'QueryVonrTableServlet2', // 此处为静态模拟数据，实际使用时需换成真实接口
            even: false,
            toolbar: '#page0',
            id: 'testReload0',
            request: {
                pageName: 'pageIndex',  // 页码的参数名称，默认：page
                limitName: 'pageSize' // 每页数据量的参数名，默认：limit
            },
            response: {
                statusName: 'code', // 规定数据状态的字段名称，默认：code
                statusCode: 0, // 规定成功的状态码，默认：0
                countName: 'count', // 规定数据总数的字段名称，默认：count
                msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
            },
            cols: [[
                { field: 'dataid', title: '任务ID', width: 80 },
                { field: 'rowId', title: '子任务ID', width: 80 },
                { field: 'status', title: '状态', width: 120, templet: function (d) {
                    if (d.status === "success") {
                        return '<span style="color: green;">' + d.status + '</span>';
                    } else if (d.status === "fail") {
                        return '<span style="color: red;">' + d.status + '</span>';
                    }
                } },
                { field: 'start', title: '开始时间', width: 200 },
                { field: 'end', title: '结束时间', width: 200 },
                { field: 'filename', title: '文件名', width: 300 },
                { field: 'result', title: '结果', width: 150 },
                { field: 'lossInfo', title: '详情', width: 250 },
                { field: 'operate', title: '操作', width: 150, toolbar: '#barDemo' },
            ]],
            done: function (res, curr, count) {
                bindMainTableToolEvent();
                     var that = $("#table0").siblings();
                     that.find(".layui-table-body").css("height","auto");
                     that.find(".layui-table-body").css("max-height","600px");
                     that.find(".layui-table-body").css("overflow-y","auto");
            },
            page: {
                layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                curr: 1,      // 设置默认起始页1
                groups: 5,   //只显示10个连续页码,就是说显示10个可见页其他的省略
                first: false, // 不显示首页
                last: false   // 不显示尾页
            },
            text: {
                none: '暂无相关数据'
            },
            limit: 20,
            limits: [5, 10, 15, 20, 100, 1000]
        });
    }

    // 渲染log2表格
    function renderLog2Table() {
        table.render({
            type: 'post',
            elem: '#table2',
            url: 'QueryVonrLog2TableServlet',
            even: false,
            id: 'testReload2',
            request: {
                pageName: 'pageIndex',  // 页码的参数名称，默认：page
                limitName: 'pageSize' // 每页数据量的参数名，默认：limit
            },
            response: {
                statusName: 'code', // 规定数据状态的字段名称，默认：code
                statusCode: 0, // 规定成功的状态码，默认：0
                countName: 'count', // 规定数据总数的字段名称，默认：count
                msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
            },
            cols: [[
                { title: 'ID', width: 80,type:"numbers" },
                { field: 'userid', title: '用户ID', width: 120 },
//                { field: 'filename', title: '文件名', width: 250 },
                { field: 'uploadtime', title: '上传时间', width: 180 },
                { field: 'status', title: '状态', width: 180 },
//                { field: 'status', title: '状态', width: 100, templet: function (d) {
//                    if (d.status === "success") {
//                        return '<span style="color: green;">成功</span>';
//                    } else if (d.status === "fail") {
//                        return '<span style="color: red;">失败</span>';
//                    } else {
//                        return '<span style="color: orange;">处理中</span>';
//                    }
//                } },
                { field: 'taskid', title: '任务ID', width: 200 },
                { field: 'filename', title: '文件', width: 180 },
                { field: 'query', title: '查询操作', width: 120, toolbar: '#logQueryBar' }
            ]],
            page: {
                layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                curr: 1,      // 设置默认起始页1
                groups: 5,   //只显示5个连续页码
                first: false, // 不显示首页
                last: false   // 不显示尾页
            },
            text: {
                none: '暂无相关数据'
            },
            limit: 20,
            limits: [5, 10, 15, 20, 100, 1000]
        });
    }

    // 绑定主表格工具栏事件
    function bindMainTableToolEvent() {
        table.on('tool(table0)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            var tr = obj.tr;

            if (layEvent === 'detail') {
                // 检查是否已经展开
                var nextTr = tr.next();
                if (nextTr.hasClass('collapsible-row')) {
                    // 已展开，收起
                    nextTr.remove();
                    return;
                }

                // 创建子表格ID
                subTableId = 'subTable_' + data.dataid;

                // 创建子表格行
                var subTableHtml = '<tr class="collapsible-row"><td colspan="9"><div class="collapsible-content" style="display:none;"><div id="' + subTableId + '"></div></div></td></tr>';
                tr.after(subTableHtml);

                // 渲染子表格
                if (data.subData && data.subData.length > 0) {
                    // 渲染子表格
                    table.render({
                        elem: `#${subTableId}`,
                        cols: [[
                            { field: 'dataid', title: '任务ID', width: 80 },
                            { field: 'rowId', title: '子任务ID', width: 80 },
                            { field: 'status', title: '状态', width: 120, templet: function (d) {
                                if (d.status === "success") {
                                    return '<span style="color: green;">' + d.status + '</span>';
                                } else if (d.status === "fail") {
                                    return '<span style="color: red;">' + d.status + '</span>';
                                }
                            } },
                            { field: 'start', title: '开始时间', width: 200 },
                            { field: 'end', title: '结束时间', width: 200 },
                            { field: 'filename', title: '文件名', width: 300 },
                            { field: 'result', title: '结果', width: 150 },
                            { field: 'lossInfo', title: '详情', width: 250 },
                            { field: 'operate', title: '操作', width: 150, toolbar: '#bardown1' },
                        ]],
                        data: data.subData,
                        done: function() {
                            // 确保子表格渲染完成后显示
                            $(`#${subTableId}`).closest('.collapsible-content').show();
                        }
                    });
                } else {
                    // 没有子数据时显示提示
                    $(`#${subTableId}`).html('<div style="text-align: center; padding: 20px; color: #999;">暂无子任务数据</div>');
                    $(`#${subTableId}`).closest('.collapsible-content').show();
                }
            }
        });
    }

    // 查询并跳转函数
    function queryAndJump(data) {
        layer.load(1, {
            shade: [0.3, '#000']
        });

        $.ajax({
            url: 'QueryVonrInfoServlet',
            type: 'POST',
            data: {
                taskid: data.taskid
            },
            success: function(res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    var reporturl = res.reporturl;
                    
                    if(reporturl!=""){
	                    window.open(reporturl, '_blank');
	                    table.reload('testReload2');
					}else{
						layer.msg('暂无分析报告生成', {icon: 2});
						table.reload('testReload2');
					}
                } else {
                    layer.msg('查询失败：' + (res.msg || '未知错误'), {icon: 2});
                    table.reload('testReload2');

                }
            },
            error: function() {
                layer.closeAll('loading');
                layer.msg('查询请求异常', {icon: 2});
            }
        });
    }

    // 初始化后台API文件上传功能
    function initApiUpload() {
        var selectedWngFiles = [];
        var selectedAudioFiles = [];

        // WNG文件选择
        upload.render({
            elem: '#selectWngFilesBtn',
            url: '', // 这里不设置url，因为我们要手动处理
            auto: false, // 不自动上传
            multiple: true, // 允许多选
            accept: 'file', // 允许所有文件类型
            exts: 'wng', // 只允许wng文件
            choose: function(obj) {
                var files = obj.pushFile();
                // 将新选择的文件添加到数组中
                Object.keys(files).forEach(function(index) {
                    selectedWngFiles.push(files[index]);
                });
                updateFileCount('#wngFileCount', selectedWngFiles.length, 'WNG');
                updateUploadButton();
            }
        });

        // Audio文件选择
        upload.render({
            elem: '#selectAudioFilesBtn',
            url: '', // 这里不设置url，因为我们要手动处理
            auto: false, // 不自动上传
            multiple: true, // 允许多选
            accept: 'audio', // 音频文件
            choose: function(obj) {
                var files = obj.pushFile();
                // 将新选择的文件添加到数组中
                Object.keys(files).forEach(function(index) {
                    selectedAudioFiles.push(files[index]);
                });
                updateFileCount('#audioFileCount', selectedAudioFiles.length, 'Audio');
                updateUploadButton();
            }
        });

        // 更新文件计数显示
        function updateFileCount(selector, count, type) {
            if (count > 0) {
                $(selector).text('已选择 ' + count + ' 个' + type + '文件')
                    .css('color', '#5FB878')
                    .addClass('selected');
            } else {
                $(selector).text('未选择文件')
                    .css('color', '#666')
                    .removeClass('selected');
            }
        }

        // 更新上传按钮状态
        function updateUploadButton() {
            if (selectedWngFiles.length > 0 && selectedAudioFiles.length > 0) {
                $('#uploadFilesBtn').removeClass('layui-btn-primary').addClass('layui-btn-normal').prop('disabled', false);
            } else {
                $('#uploadFilesBtn').removeClass('layui-btn-normal').addClass('layui-btn-primary').prop('disabled', true);
            }
        }

        // 清空WNG文件
        $('#clearWngFilesBtn').on('click', function() {
            selectedWngFiles = [];
            updateFileCount('#wngFileCount', 0, 'WNG');
            updateUploadButton();
        });

        // 清空Audio文件
        $('#clearAudioFilesBtn').on('click', function() {
            selectedAudioFiles = [];
            updateFileCount('#audioFileCount', 0, 'Audio');
            updateUploadButton();
        });

        // 上传文件
        $('#uploadFilesBtn').on('click', function() {
            // 验证文件选择
            if (selectedWngFiles.length === 0 || selectedAudioFiles.length === 0) {
                layer.msg('请先选择WNG文件和Audio文件', {icon: 2});
                return;
            }

            // 获取文件摘要信息
            var summary = getFilesSummary(selectedWngFiles, selectedAudioFiles);
            console.log('文件上传摘要:', summary);

            // 显示确认对话框
            var confirmMsg = '即将上传：<br/>' +
                           'WNG文件: ' + summary.wngCount + '个<br/>' +
                           'Audio文件: ' + summary.audioCount + '个<br/>' +
                           '总大小: ' + summary.totalSizeFormatted + '<br/>' +
                           '是否继续？';

            layer.confirm(confirmMsg, {
                icon: 3,
                title: '确认上传'
            }, function(index) {
                layer.close(index);
                performUpload();
            });
        });

        // 执行上传
        function performUpload() {
            // 显示加载层和进度条
            var loadingIndex = layer.load(1, {
                shade: [0.3, '#000']
            });
            showProgress();

            // 获取登录信息
            var loginInfo = islogininfo();
            var loginParts = loginInfo.split("#");
            var employeeId = loginParts[0];
            var userName = loginParts[1];
            var token = loginParts[2];

            console.log('登录信息:', {employeeId: employeeId, userName: userName, token: token});

            // 创建FormData
            var formData = new FormData();
            formData.append('employee_id', employeeId);
            formData.append('token', token);

            // 添加WNG文件
            for (var i = 0; i < selectedWngFiles.length; i++) {
                formData.append('wng_files', selectedWngFiles[i]);
            }

            // 添加Audio文件
            for (var i = 0; i < selectedAudioFiles.length; i++) {
                formData.append('audio_files', selectedAudioFiles[i]);
            }

            // 调用后台Servlet
            $.ajax({
                url: 'ApiUploadServlet',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                timeout: 300000, // 5分钟超时
                success: function(result) {
                    layer.close(loadingIndex);
                    hideProgress();

                    if (result.code === 0) {
                        layer.msg('上传成功！任务ID: ' + result.task_id, {icon: 1, time: 4000});

                        // 清空选择的文件
                        selectedWngFiles = [];
                        selectedAudioFiles = [];
                        updateFileCount('#wngFileCount', 0, 'WNG');
                        updateFileCount('#audioFileCount', 0, 'Audio');
                        updateUploadButton();

                        // 刷新上传结果记录表格
                        if (window.log2TableRendered) {
                            table.reload('testReload2');
                        } else {
                            renderLog2Table();
                            window.log2TableRendered = true;
                        }
                    } else {
                        layer.msg('上传失败：' + (result.msg || '未知错误'), {icon: 2, time: 5000});
                    }
                },
                error: function(xhr, status, error) {
                    layer.close(loadingIndex);
                    hideProgress();

                    var errorMsg = '上传失败：';
                    if (status === 'timeout') {
                        errorMsg += '请求超时';
                    } else if (xhr.status === 0) {
                        errorMsg += '网络连接失败';
                    } else {
                        errorMsg += 'HTTP ' + xhr.status + ' - ' + error;
                    }

                    console.error('上传错误:', xhr, status, error);
                    layer.msg(errorMsg, {icon: 2, time: 5000});
                }
            });
        }

        // 获取文件摘要信息
        function getFilesSummary(wngFiles, audioFiles) {
            var wngCount = wngFiles ? wngFiles.length : 0;
            var audioCount = audioFiles ? audioFiles.length : 0;

            var totalSize = 0;
            if (wngFiles) {
                for (var i = 0; i < wngFiles.length; i++) {
                    totalSize += wngFiles[i].size;
                }
            }
            if (audioFiles) {
                for (var i = 0; i < audioFiles.length; i++) {
                    totalSize += audioFiles[i].size;
                }
            }

            return {
                wngCount: wngCount,
                audioCount: audioCount,
                totalSize: totalSize,
                totalSizeFormatted: formatFileSize(totalSize)
            };
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes < 1024) {
                return bytes + ' B';
            } else if (bytes < 1024 * 1024) {
                return (bytes / 1024).toFixed(1) + ' KB';
            } else if (bytes < 1024 * 1024 * 1024) {
                return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
            } else {
                return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
            }
        }
    }

    // 显示进度条
    function showProgress() {
        $('#progressContainer').show();
        updateProgress(0);
    }

    // 隐藏进度条
    function hideProgress() {
        $('#progressContainer').hide();
        updateProgress(0);
    }

    // 更新进度条
    function updateProgress(percent) {
        $('#progressBar').css('width', percent + '%');
        $('#progressText').text(percent + '%');
    }

    // 监听log2表格工具栏事件
    table.on('tool(table2)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        switch (layEvent) {
            case 'query':
                queryAndJump(data);
                break;
        }
    });

    // 监听tab切换事件
    element.on('tab(mainTab)', function(data){
        console.log('切换到第' + (data.index + 1) + '个tab');
        if(data.index === 1) {
            // 切换到前端直连上传tab时，渲染log2表格（延迟渲染，避免影响性能）
            if(!window.log2TableRendered) {
                setTimeout(function() {
                    renderLog2Table();
                    window.log2TableRendered = true;
                }, 200);
            }
        }
    });

    // 页面加载完成后初始化
    $(document).ready(function() {
        // 渲染主表格
        renderMainTable();

        // 初始化后台API文件上传功能
        initApiUpload();

        // 标记表格还未渲染
        window.log2TableRendered = false;
        window.uploadInitialized = false;
    });

});

function islogininfo(){
	var qqq="",www="",name="",token="";
	var idss = document.cookie.split(";");
	for(var i=0;i< idss.length;i++){
		var xx = idss[i];
		qqq=xx.split("=")[0];
		qqq=qqq.trim();
		if(qqq=="info")
		{
			www=xx.split("=")[1].split("#")[0];
			name=xx.split("=")[1].split("#")[1];
			token=xx.split("=")[1].split("#")[2];

		}
	}
		www = "10306639";
		name="陶森";
		token="72fbd8924d91aa52f9383328f424b139";
	return www+"#"+name+"#"+token;
}
