var xmlHttp=false;  

 function createXMLHttpRequest() {
     if (window.ActiveXObject){
           xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
     }
     else if (window.XMLHttpRequest){
           xmlHttp = new XMLHttpRequest();
      }  
 }
var dev;
var ind;
function init(version,contrastTestVersion,devname,team,index) 
{	
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = initcallBack;
    team=encodeURIComponent(team);
    team=encodeURIComponent(team);
    dev=devname;
    ind=index;
    var url="QueryKpiInfoServlet?version="+version+"&devname="+devname+"&team="+team+"&contrastTestVersion="+contrastTestVersion+"&index="+index;  
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}

function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
			table1(data[0]);
			var c  = document.getElementById("chart") ; 
			chart(c,data[1],data[2],data[3]);
			table2(data[4]);
            }
     }
}

function table1(data){
	var tbody1  = document.getElementById("tbody1") ; 
	s="";
	for(var i=0;i<data.length;i++){
		if(data[i].comparison=="恶化项"){
			s+="<tr ondblclick='tapcounter()' bgcolor='#FF9797'>";
		}
		else if(data[i].comparison=="提升项"){
			s+="<tr ondblclick='tapcounter()' bgcolor='#A6FFA6'>";
		}
		else{
			s+="<tr ondblclick='tapcounter()' bgcolor='#ACD6FF'>";
		}
		s+="<td style='text-align:center'>"+data[i].level+"</td>";
		s+="<td style='text-align:center'>"+data[i].daihao+"</td>";
		s+="<td style='text-align:center'>"+data[i].before+"</td>";
		s+="<td style='text-align:center'>"+data[i].after+"</td>";
		s+="<td style='text-align:center'>"+data[i].chazhi+"</td>";
		s+="<td style='text-align:center'>"+data[i].fudu+"</td>";
		s+="<td style='text-align:center'>"+data[i].comparison+"</td>";
		if(data[i].comparison=="恶化项"){
			s+="<td style='text-align:center'>"+data[i].koufen+"</td>";
		}else{
			s+="<td style='text-align:center'>"+""+"</td>";
		}
		
		s+="</tr>"
	}
	tbody1.innerHTML=s;
}

function table2(data){
	if(ind==1||ind==2){
		document.getElementById("info").style.display="block" ; 
		document.getElementById("info2").style.display="none" ; 
		var tbody1  = document.getElementById("infotbody1") ; 
		s="";
		if(data[9]=="不通过"){
			s+="<tr bgcolor='#ef7a82'>";
		}
		else{
			s+="<tr>";
		}
		for(var i=0;i<data.length;i++){
			s+="<td style='text-align:center'>"+data[i]+"</td>";
		}
		s+="</tr>"
		tbody1.innerHTML=s;
	}else{
		document.getElementById("info").style.display="none" ; 
		document.getElementById("info2").style.display="block" ; 
		var tbody1  = document.getElementById("infotbody2") ; 
		s="";
		if(data[9]=="不通过"){
			s+="<tr bgcolor='#ef7a82'>";
		}
		else{
			s+="<tr>";
		}
		for(var i=0;i<data.length;i++){
			s+="<td style='text-align:center'>"+data[i]+"</td>";
		}
		s+="</tr>"
		tbody1.innerHTML=s;
	}
	
}

function chart(divname,data1,data2,data3){
	var chart = Highcharts.chart(divname, {
		chart: {
				zoomType: 'xy',
		},
		title: {
				text: 'KPI指标情况',
				style:{
						fontSize:'30px'
					}

		},

		xAxis: [{
				categories: ["关键一级","关键二级","关键三级","内部","其它"],
				crosshair: true,
				labels: {
					
					style:{
						fontSize:'20px'
					}
						//rotation:-30
				}
				
		}],
		yAxis: [
		{
				gridLineWidth: 0,
				title: {
						text: '',
				},
				labels: {
						format: '{value} ',
						style:{
						fontSize:'20px'
					}
				}		
		}
		],
		tooltip: {
				shared: true
		},
		legend: {
				layout: 'vertical',
				align: 'left',
				x: 80,
				verticalAlign: 'top',
				y: 55,
				floating: true,
				backgroundColor: (Highcharts.theme && Highcharts.theme.legendBackgroundColor) || '#FFFFFF'
		},
		plotOptions: {
        column: {
			maxPointWidth: 80,
            //stacking: 'normal',
            dataLabels: {
                enabled: true,
                color: (Highcharts.theme && Highcharts.theme.dataLabelsColor) || 'white',
                style: {
                    // 如果不需要数据标签阴影，可以将 textOutline 设置为 'none'
                    textOutline: '1px 1px black'
                }
            }
        }
    },
		series: [ 
		{
				name: '改善项',
				type: 'column',
				yAxis: 0,
				data:data1,
				marker: {
						enabled: false
				},
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: ' '
				},
				color: '#A6FFA6'
		},
		{
				name: '持平项',
				type: 'column',
				yAxis: 0,
				data:data2,
				marker: {
						enabled: false
				},
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: ' '
				},
				color: '#ACD6FF'
		},
		{
				name: '恶化项',
				type: 'column',
				yAxis: 0,
				data:data3,
				marker: {
						enabled: false
				},
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: ' '
				},
				color: 'red'
		}]
});
	
}

function tapcounter(){
	if(ind==1 || ind ==2){
		var td = event.srcElement;
		tr =td.parentElement; 
		var index = tr.rowIndex-1;
		window.open ("kpicounter.jsp?index="+index+"&dev="+dev, "newwindow2", "height=500, width=1200, top=100, left=150,toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no")
	}else{
		
	}


}
