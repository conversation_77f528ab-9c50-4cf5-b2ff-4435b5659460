/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableTreegrid={exports:{}}.exports}})(this,function(){'use strict';!function(a){a.extend(a.fn.bootstrapTable.defaults,{treeShowField:null,idField:'id',parentIdField:'pid',rootParentId:null,onGetNodes:function(b,c){var d=this,e=[];return a.each(c,function(a,c){b[d.options.idField]===c[d.options.parentIdField]&&e.push(c)}),e},onCheckRoot:function(a){var b=this;return b.options.rootParentId===a[b.options.parentIdField]||!a[b.options.parentIdField]}});var b=a.fn.bootstrapTable.Constructor,c=b.prototype.init,d=b.prototype.initRow,e=b.prototype.initHeader,f=null;b.prototype.init=function(){f=this.options.rowStyle,c.apply(this,Array.prototype.slice.apply(arguments))},b.prototype.initHeader=function(){var b=this;e.apply(b,Array.prototype.slice.apply(arguments));var c=b.options.treeShowField;c&&a.each(this.header.fields,function(a,d){if(c===d)return b.treeEnable=!0,!1})};var g=function(b,c,e,h){var j=this,k=j.options.onGetNodes.apply(j,[b,e]);b._nodes=k,h.append(d.apply(j,[b,c,e,h]));for(var l,m=k.length-1,n=0;n<=m;n++)l=k[n],l._level=b._level+1,l._parent=b,n===m&&(l._last=1),j.options.rowStyle=function(a){var b=f.apply(j,Array.prototype.slice.apply(arguments)),c=a[j.options.idField]?a[j.options.idField]:0,d=a[j.options.parentIdField]?a[j.options.parentIdField]:0;return b.classes=[b.classes||'','treegrid-'+c,'treegrid-parent-'+d].join(' '),b},g.apply(j,[l,a.inArray(l,e),e,h])};b.prototype.initRow=function(a,b,c,e){var h=this;return h.treeEnable?!!h.options.onCheckRoot.apply(h,[a,c])&&(void 0===a._level&&(a._level=0),h.options.rowStyle=function(a){var b=f.apply(h,Array.prototype.slice.apply(arguments)),c=a[h.options.idField]?a[h.options.idField]:0;return b.classes=[b.classes||'','treegrid-'+c].join(' '),b},g.apply(h,[a,b,c,e]),!0):d.apply(h,Array.prototype.slice.apply(arguments))}}(jQuery)});