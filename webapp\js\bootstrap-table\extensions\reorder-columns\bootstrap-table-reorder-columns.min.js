/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableReorderColumns={exports:{}}.exports}})(this,function(){'use strict';!function(a){var b=function(){Array.prototype.filter||(Array.prototype.filter=function(a){if(void 0===this||null===this)throw new TypeError;var b=Object(this),c=b.length>>>0;if('function'!=typeof a)throw new TypeError;for(var d=[],e=2<=arguments.length?arguments[1]:void 0,f=0;f<c;f++)if(f in b){var g=b[f];a.call(e,g,f,b)&&d.push(g)}return d})};a.extend(a.fn.bootstrapTable.defaults,{reorderableColumns:!1,maxMovingRows:10,onReorderColumn:function(){return!1},dragaccept:null}),a.extend(a.fn.bootstrapTable.Constructor.EVENTS,{"reorder-column.bs.table":'onReorderColumn'});var c=a.fn.bootstrapTable.Constructor,d=c.prototype.initHeader,e=c.prototype.toggleColumn,f=c.prototype.toggleView,g=c.prototype.resetView;c.prototype.initHeader=function(){d.apply(this,Array.prototype.slice.apply(arguments));this.options.reorderableColumns&&this.makeRowsReorderable()},c.prototype.toggleColumn=function(){e.apply(this,Array.prototype.slice.apply(arguments));this.options.reorderableColumns&&this.makeRowsReorderable()},c.prototype.toggleView=function(){f.apply(this,Array.prototype.slice.apply(arguments));!this.options.reorderableColumns||this.options.cardView||this.makeRowsReorderable()},c.prototype.resetView=function(){g.apply(this,Array.prototype.slice.apply(arguments));this.options.reorderableColumns&&this.makeRowsReorderable()},c.prototype.makeRowsReorderable=function(){var c=this;try{a(this.$el).dragtable('destroy')}catch(a){}a(this.$el).dragtable({maxMovingRows:c.options.maxMovingRows,dragaccept:c.options.dragaccept,clickDelay:200,beforeStop:function(){var d=[],e=[],f=[],g=[],h=-1,j=[];if(c.$header.find('th').each(function(){d.push(a(this).data('field')),e.push(a(this).data('formatter'))}),d.length<c.columns.length){g=a.grep(c.columns,function(a){return!a.visible});for(var k=0;k<g.length;k++)d.push(g[k].field),e.push(g[k].formatter)}for(var k=0;k<this.length;k++)h=c.fieldsColumnsIndex[d[k]],-1!==h&&(c.columns[h].fieldIndex=k,f.push(c.columns[h]),c.columns.splice(h,1));c.columns=c.columns.concat(f),b(),a.each(c.columns,function(a,b){var d=!1,e=b.field;c.options.columns[0].filter(function(a){return d||a.field!=e||(j.push(a),d=!0,!1)})}),c.options.columns[0]=j,c.header.fields=d,c.header.formatters=e,c.initHeader(),c.initToolbar(),c.initBody(),c.resetView(),c.trigger('reorder-column',d)}})}}(jQuery)});