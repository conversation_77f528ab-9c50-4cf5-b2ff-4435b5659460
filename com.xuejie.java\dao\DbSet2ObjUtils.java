package dao;

import java.lang.reflect.Field;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 Title:DbRet2ObjUtils
 Description:锟斤拷菘锟斤拷询锟斤拷锟絉esultSet转锟斤拷为锟斤拷锟斤拷锟叫憋拷
 Auther:薛锟斤拷10170365
 Date:2017-11-2锟斤拷锟斤拷12:01:22
 */
public class DbSet2ObjUtils {
	
	 /*
     * 锟斤拷rs锟斤拷锟阶拷锟斤拷啥锟斤拷锟斤拷斜锟�
     * @param rs jdbc锟斤拷锟�
     * @param clazz 锟斤拷锟斤拷锟接筹拷锟斤拷锟�
     * return 锟斤拷装锟剿讹拷锟斤拷慕锟斤拷锟叫憋拷
     */
    public static List<Object> populate(ResultSet rs , Class<?> clazz) throws SQLException, InstantiationException, IllegalAccessException{
        //锟斤拷锟斤拷元锟截讹拷锟斤拷 
        ResultSetMetaData rsmd = rs.getMetaData();
        //锟斤拷取锟斤拷锟斤拷元锟截革拷锟斤拷
         int colCount = rsmd.getColumnCount();
         //锟斤拷锟截斤拷锟斤拷锟叫�?锟斤拷
         List<Object> list = new ArrayList<Object>();
         //业锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷
         Field[] fields = clazz.getDeclaredFields();
         while(rs.next()){//锟斤拷每一锟斤拷锟斤拷录锟斤拷锟叫诧拷锟斤拷
             Object obj = clazz.newInstance();//锟斤拷锟斤拷业锟斤拷锟斤拷锟绞碉拷锟�
             //锟斤拷每一锟斤拷锟街讹拷取锟斤拷锟斤拷锟叫革拷值
             for(int i = 1;i<=colCount;i++){
                 Object value = rs.getObject(i);
                 //寻锟揭革拷锟叫讹拷应锟侥讹拷锟斤拷锟斤拷锟斤拷
                 for(int j=0;j<fields.length;j++){
                     Field f = fields[j];
                     //锟斤拷锟狡ワ拷锟斤拷锟叫革拷值
                     if(f.getName().equalsIgnoreCase(rsmd.getColumnName(i))){
                         boolean flag = f.isAccessible();
                         f.setAccessible(true);
                         f.set(obj, value);
                         f.setAccessible(flag);
                     }
                 }
             }
             list.add(obj);
         }
        return list;
    }
    
    

}
