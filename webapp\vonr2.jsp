<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>iWork自动化测试</title>
<meta name="author" content="xuejie10170365">
<link href="css/bootstrap.min.css" rel="stylesheet">
<link href="css/materialdesignicons.min.css" rel="stylesheet">
<link href="css/style.min.css" rel="stylesheet">
<link href="addtask/layui-v2.8.3/layui/css/layui.css" type="text/css" rel="stylesheet">
<link href="css/layer/layer.css" type="text/css" rel="stylesheet">

</head>
<style>
	.collapsible-content thead {
    display: none;
}
.collapsible-content {
    display: none;
}
/* .collapsible-row { */
/*     background-color: #f0f8ff; /* 此处颜色可根据喜好调整，这是淡蓝色 */ */
/* } */
</style>
<body>
<div class="container-fluid p-t-15">

  <div class="row">
    <div class="col-md-12">
      <div class="card">
      <!-- 上传按钮区域 -->
<!--       <div style="position: absolute; top: 20px; left: 20px; z-index: 1000;"> -->
 
<!--       </div> -->

      <center style="padding-top: 20px">
              <a style="font-size: 30px;font-weight: 500;">AI智能语音分析看板<span class="layui-badge layui-bg-blue">iWork</span></a>        <div class="card-body">

      </center>
<!--                <button type="button" class="layui-btn layui-btn-sm layui-btn-radius layui-bg-green" id="uploadMp3Btn"> -->
<!--               <i class="layui-icon layui-icon-upload"></i> 上传MP3文件 -->
<!--           </button> -->
      				<a onclick="tap1()" style="display:inline-block;float:right;margin-right: 10px" class="layui-btn layui-btn-sm layui-btn-radius layui-btn-normal" data-animation="fadeInUp" data-delay=".6s">测试工具箱(公网)</a>
<!-- 					<a onclick="tap2()" style="display:inline-block;float:right;margin-right: 10px" class="layui-btn layui-btn-sm layui-btn-radius layui-btn-normal" data-animation="fadeInUp" data-delay=".6s">测试工具箱(深圳)</a> -->
<!--    				    <a onclick="tap3()" style="display:inline-block;float:right;" class="layui-btn layui-btn-sm layui-btn-radius layui-btn-normal" data-animation="fadeInUp" data-delay=".6s">测试工具箱(西安)</a> -->

          <!-- Tab导航 -->
          <div class="layui-tab layui-tab-brief" lay-filter="mainTab">
            <ul class="layui-tab-title">
              <li class="layui-this">语音分析记录</li>
              <li>文件上传日志</li>
            </ul>
            <div class="layui-tab-content">
              <!-- 第一个tab：语音分析记录 -->
              <div class="layui-tab-item layui-show">
                <table id="table0" lay-filter="lay0" class="layui-table" style="table-layout:fixed;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;">
                </table>
              </div>
              <!-- 第二个tab：文件上传日志 -->
              <div class="layui-tab-item">
                <div style="margin-bottom: 20px;">
                  <div style="font-size: 16px; font-weight: bold; margin-bottom: 10px; color: #333;">文件上传日志</div>
                  <table id="table1" lay-filter="lay1" class="layui-table" style="table-layout:fixed;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;">
                  </table>
                </div>

                <div>
                  <div style="font-size: 16px; font-weight: bold; margin-bottom: 10px; color: #333;">上传结果记录</div>
                  <table id="table2" lay-filter="lay2" class="layui-table" style="table-layout:fixed;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;">
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>

<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/main.min.js"></script>

<script type="text/javascript" src="addtask/layui-v2.8.3/layui/layui.js"></script>
<!-- <script type="text/javascript" src="js/usecase.js"></script> -->
<script type="text/javascript" src="js/vonrinfo2.js?v=7"></script>
<script type="text/html" id="page0">
  	<div class="layui-inline">
		        <input class="layui-input" name="id" id="tag0" autocomplete="off" placeholder="请输入...">
		      </div>
		      <button type="button"  id="envsearch" class="layui-btn layui-btn-sm"  lay-event="search">搜索</button>
		    </div>
</script>
<script type="text/html" id="page1">
  	<div class="layui-inline">
		        <input class="layui-input" name="id" id="tag1" autocomplete="off" placeholder="请输入文件名、用户ID等...">
		      </div>
		      <button type="button"  id="logsearch" class="layui-btn layui-btn-sm"  lay-event="search">搜索</button>
		      <button type="button"  id="batchUpload" class="layui-btn layui-btn-sm layui-bg-green"  lay-event="batchUpload">
		          <i class="layui-icon layui-icon-upload"></i> 批量上传
		      </button>
		    </div>
</script>
<script type="text/html" id="bardown1">
	<a  class="layui-btn layui-btn-xs layui-bg-blue" lay-event="start1" >在线播放</a>
</script>
<script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看详情</a>
</script>
<script type="text/html" id="logUploadBar">
    <a class="layui-btn layui-btn-xs layui-bg-green" lay-event="upload">
        <i class="layui-icon layui-icon-upload"></i> 上传
    </a>
</script>
<script type="text/html" id="logQueryBar">
    <a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="query">
        <i class="layui-icon layui-icon-search"></i> 查询
    </a>
</script>
</body>
</html>