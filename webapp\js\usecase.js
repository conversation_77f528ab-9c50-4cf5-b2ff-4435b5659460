var xmlHttp=false;  


function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}

function init(){
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = initcallBack;
    	var url="QueryUsecaseInfoServlet";  
    	xmlHttp.open("GET", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);	
}

function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
//			 layui.use(['laypage'], function(){
			     var laypage = layui.laypage;//分页
			    //以上模块根据需要引
			    laypage.render({
			        elem: 'page0'
			        ,count: data[4].ptype0
			        ,groups:5
			        ,first: 1
			        ,limit:20
			        ,prev: '<em><</em>'
			        ,next: '<em>></em>'
			        ,jump:function(obj,first) {
						changetable(obj.curr,obj.limit,0,"")
					}
			    });
//			});
//			layui.use(['laypage'], function(){
			     var laypage = layui.laypage;//分页
			    //以上模块根据需要引
			    laypage.render({
			        elem: 'page1'
			        ,count: data[4].ptype1
			        ,groups:5
			        ,first: 1
			        ,limit:20
			        ,prev: '<em><</em>'
			        ,next: '<em>></em>'
			        ,jump:function(obj,first) {
						changetable(obj.curr,obj.limit,1,"")
					}
			    });
//			});
//						 layui.use(['laypage'], function(){
			     var laypage = layui.laypage;//分页
			    //以上模块根据需要引
			    laypage.render({
			        elem: 'page2'
			        ,count: data[4].ptype2
			        ,groups:5
			        ,first: 1
			        ,limit:20
			        ,prev: '<em><</em>'
			        ,next: '<em>></em>'
			        ,jump:function(obj,first) {
						changetable(obj.curr,obj.limit,2,"")
					}
			    });
//			});
//						 layui.use(['laypage'], function(){
			     var laypage = layui.laypage;//分页
			    //以上模块根据需要引
			    laypage.render({
			        elem: 'page3'
			        ,count: data[4].ptype3
			        ,groups:5
			        ,first: 1
			        ,limit:20
			        ,prev: '<em><</em>'
			        ,next: '<em>></em>'
			        ,jump:function(obj,first) {
						changetable(obj.curr,obj.limit,3,"")
					}
			    });
//			});
			table0(data[0])
			table1(data[1])
			table2(data[2])
			table3(data[3])
			
		}
	}	
}

function changetable(page,limit,ptype,id){
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = changetablecallBack;
    	var url="QueryUsecaseInfoChangeServlet?page="+page+"&limit="+limit+"&ptype="+ptype+"&id="+id;  
    	xmlHttp.open("POST", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);
}

function changetablecallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data);
			if(data[0]==0){
				table0(data[1])
			}else if(data[0]==1){
				table1(data[1])
			}else if(data[0]==2){
				table2(data[1])
			}else if(data[0]==3){
				table3(data[1])
			}
		}
	}	
}

function table0(data){
	var tbody  = document.getElementById("tbody0") ; 
	s="";
	for(var i=0;i<data.length;i++){
		s+="<td style='text-align:center' >"+(i+1)+"</td>";
		s+="<td >"+data[i].team+"</td>";
		s+="<td >"+data[i].id+"</td>";
		s+="<td title='"+data[i].title+"' style='overflow:hidden;word-break:keep-all;white-space:nowrap;'>"+data[i].title+"</td>";
		s+="<td >"+data[i].state+"</td>";
		s+="<td >"+data[i].hasautomated+"</td>";
		s+="<td >"+data[i].author+"</td>";
		s+="<td >"+data[i].executephase+"</td>";
		s+="</tr>"
	}
	tbody.innerHTML=s;
}

function table1(data){
	var tbody  = document.getElementById("tbody1") ; 
	s="";
	for(var i=0;i<data.length;i++){
		s+="<td style='text-align:center'>"+(i+1)+"</td>";
		s+="<td >"+data[i].team+"</td>";
		s+="<td >"+data[i].id+"</td>";
		s+="<td title='"+data[i].title+"' style='overflow:hidden;word-break:keep-all;white-space:nowrap;'>"+data[i].title+"</td>";
		s+="<td >"+data[i].state+"</td>";
		s+="<td >"+data[i].hasautomated+"</td>";
		s+="<td >"+data[i].author+"</td>";
		s+="<td >"+data[i].performancetopic+"</td>";
		s+="<td >"+data[i].executephase+"</td>";
		if(data[i].parambushu==1){
			s+="<td >"+"<a class='layui-btn layui-btn-xs' onclick='x_admin_show("+data[i].id.split('-')[1]+")'>测试配置</a>"+"</td>";
		}else{
			s+="<td >"+"<a class='layui-btn layui-btn-danger layui-btn-xs' onclick='x_admin_show("+data[i].id.split('-')[1]+")'>测试配置</a>"+"</td>";
		}
		if(data[i].kpibushu==1){
			s+="<td >"+"<a class='layui-btn  layui-btn-xs' onclick='choose2("+data[i].id.split('-')[1]+")'>指标预期</a>"+"</td>";
		}else{
			s+="<td >"+"<a class='layui-btn layui-btn-danger layui-btn-xs' onclick='choose2("+data[i].id.split('-')[1]+")'>指标预期</a>"+"</td>";
		}

		s+="</tr>"
	}
	tbody.innerHTML=s;
}

function table3(data){
	var tbody  = document.getElementById("tbody3") ; 
	s="";
	for(var i=0;i<data.length;i++){
		s+="<td style='text-align:center'>"+(i+1)+"</td>";
		s+="<td >"+data[i].team+"</td>";
		s+="<td >"+data[i].id+"</td>";
		s+="<td title='"+data[i].title+"' style='overflow:hidden;word-break:keep-all;white-space:nowrap;'>"+data[i].title+"</td>";
		s+="<td >"+data[i].state+"</td>";
		s+="<td >"+data[i].hasautomated+"</td>";
		s+="<td >"+data[i].author+"</td>";
		s+="<td >"+data[i].executephase+"</td>";
//		s+="<td >"+data[i].hasautomated+"</td>";
		s+="</tr>"
	}
	tbody.innerHTML=s;
}

function table2(data){
	var tbody  = document.getElementById("tbody2") ; 
	s="";
	for(var i=0;i<data.length;i++){
		s+="<td style='text-align:center'>"+(i+1)+"</td>";
		s+="<td >"+data[i].team+"</td>";
		s+="<td >"+data[i].id+"</td>";
		s+="<td title='"+data[i].title+"' style='overflow:hidden;word-break:keep-all;white-space:nowrap;'>"+data[i].title+"</td>";
		s+="<td >"+data[i].state+"</td>";
		s+="<td >"+data[i].hasautomated+"</td>";
		s+="<td >"+data[i].author+"</td>";
		s+="<td >"+data[i].performancetopic+"</td>";
		s+="<td >"+data[i].executephase+"</td>";
		if(data[i].parambushu==1){
			s+="<td >"+"<a class='layui-btn layui-btn-xs' onclick='x_admin_show("+data[i].id.split('-')[1]+")'>测试配置</a>"+"</td>";
		}else{
			s+="<td >"+"<a class='layui-btn layui-btn-danger layui-btn-xs' onclick='x_admin_show("+data[i].id.split('-')[1]+")'>测试配置</a>"+"</td>";
		}
		if(data[i].kpibushu==1){
			s+="<td >"+"<a class='layui-btn  layui-btn-xs' onclick='choose2("+data[i].id.split('-')[1]+")'>指标预期</a>"+"</td>";
		}else{
			s+="<td >"+"<a class='layui-btn layui-btn-danger layui-btn-xs' onclick='choose2("+data[i].id.split('-')[1]+")'>指标预期</a>"+"</td>";
		}
		s+="</tr>"
	}
	tbody.innerHTML=s;
}

function x_admin_show(title,url){
	if (title == null || title == '') {
		title=false;
	};
	if (url == null || url == '') {
		url="404.html";
	};	
    var layer=layui.layer
	layer.open({
		type: 2,
		area: ['80%', '80%'],		
		fix: false, //不固定
		maxmin: true,
		shadeClose: true,
		shade:0.4,
		title: title,
		content: url
	});
}

function x_admin_show(id){
    var layer=layui.layer
	layer.open({
		type: 2,
		//area: [w+'px', h +'px'],
		area: ['80%', '80%'],		
		fix: false, //不固定
		maxmin: true,
		shadeClose: false,
		shade:0.4,
		title: "测试配置",
//		yes:"self.focus()",
		content:"configusecase.jsp?id="+id
	});
}

var myjson;
function choose(id)
{
	const t = {taskid:id,prefix:"ZXPFM"};
	myjson = JSON.stringify(t);	
    var layer=layui.layer
	layer.open({
		type: 2,
		//area: [w+'px', h +'px'],
		area: ['95%', '95%'],		
		fix: false, //不固定
		maxmin: true,
		shadeClose: true,
		shade:0.4,
		title: "",
		content:"http://***********:8080/Iwork2Enb/templateadd.jsp"
	});
}

function choose2(id)
{
	const t = {taskid:id,prefix:"ZXPFM"};
	myjson = JSON.stringify(t);	
    var layer=layui.layer
	layer.open({
		type: 2,
		//area: [w+'px', h +'px'],
		area: ['95%', '95%'],		
		fix: false, //不固定
		maxmin: true,
		shadeClose: true,
		shade:0.4,
		title: "",
		content:"http://***********:8080/Iwork2Enb/templateaddci3.jsp"
	});
}

function tap0(){
	var id  = document.getElementById("usecase0").value ;
	if(id==""){
		init();
	}else{
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = tap0callBack;
    	var url="QueryUsecaseInfoChangeServlet?page="+1+"&limit="+20+"&ptype="+0+"&id="+id;  
    	xmlHttp.open("GET", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);
	}
}

function tap0callBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
            var id  = document.getElementById("usecase0").value ;
          	layui.use(['laypage'], function(){
			     var laypage = layui.laypage;//分页
			    //以上模块根据需要引
			    laypage.render({
			        elem: 'page0'
			        ,count: data[2]
			        ,groups:5
			        ,first: 1
			        ,limit:20
			        ,prev: '<em><</em>'
			        ,next: '<em>></em>'
			        ,jump:function(obj,first) {
						changetable(obj.curr,obj.limit,0,id)
					}
			    });
			});
            table0(data[1]);
           }
        } 
}

function tap1(){
	var id  = document.getElementById("usecase1").value ; 
	if(id==""){
		init();
	}else{
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = tap1callBack;
    	var url="QueryUsecaseInfoChangeServlet?page="+1+"&limit="+20+"&ptype="+1+"&id="+id;  
    	xmlHttp.open("GET", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);
	}
}

function tap1callBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
            var id  = document.getElementById("usecase1").value ;
          	layui.use(['laypage'], function(){
			     var laypage = layui.laypage;//分页
			    //以上模块根据需要引
			    laypage.render({
			        elem: 'page1'
			        ,count: data[2]
			        ,groups:5
			        ,first: 1
			        ,limit:20
			        ,prev: '<em><</em>'
			        ,next: '<em>></em>'
			        ,jump:function(obj,first) {
						changetable(obj.curr,obj.limit,1,id)
					}
			    });
			});
            table1(data[1]);
           }
        } 
}

function tap2(){
	var id  = document.getElementById("usecase2").value ; 
	if(id==""){
		init();
	}else{
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = tap2callBack;
    	var url="QueryUsecaseInfoChangeServlet?page="+1+"&limit="+20+"&ptype="+2+"&id="+id;  
    	xmlHttp.open("GET", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);
	}
}

function tap2callBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
            var id  = document.getElementById("usecase2").value ;
          	layui.use(['laypage'], function(){
			     var laypage = layui.laypage;//分页
			    //以上模块根据需要引
			    laypage.render({
			        elem: 'page2'
			        ,count: data[2]
			        ,groups:5
			        ,first: 1
			        ,limit:20
			        ,prev: '<em><</em>'
			        ,next: '<em>></em>'
			        ,jump:function(obj,first) {
						changetable(obj.curr,obj.limit,2,id)
					}
			    });
			});
            table2(data[1]);
           }
        } 
}

function tap3(){
	var id  = document.getElementById("usecase3").value ; 
	if(id==""){
		init();
	}else{
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = tap3callBack;
    	var url="QueryUsecaseInfoChangeServlet?page="+1+"&limit="+20+"&ptype="+3+"&id="+id;  
    	xmlHttp.open("GET", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);
	}
}

function tap3callBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
            var id  = document.getElementById("usecase3").value ;
          	layui.use(['laypage'], function(){
			     var laypage = layui.laypage;//分页
			    //以上模块根据需要引
			    laypage.render({
			        elem: 'page3'
			        ,count: data[2]
			        ,groups:5
			        ,first: 1
			        ,limit:20
			        ,prev: '<em><</em>'
			        ,next: '<em>></em>'
			        ,jump:function(obj,first) {
						changetable(obj.curr,obj.limit,3,id)
					}
			    });
			});
            table3(data[1]);
           }
        } 
}


