package servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;


/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryVonrTableSearchServlet2")
public class QueryVonrTableSearchServlet2 extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryVonrTableSearchServlet2() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		
		String page = request.getParameter("pageIndex");
		String id = request.getParameter("search_data");
		String limit = request.getParameter("pageSize");
		id = java.net.URLDecoder.decode(id,"utf-8");
		
		int limit2 = Integer.valueOf(limit);
		int limit1 =(Integer.valueOf(page)-1)*limit2; 
		
		DaoCMCC dao = new DaoCMCC();
		
        JSONArray result = new JSONArray();
		JSONArray jsonArray0 = new JSONArray();
		int count=0;
		
		
		

		if(id.equals("")) {
			String sql0="SELECT * FROM aivonrrecord2 where rowId ='total' order by dataid+0 desc limit "+limit1+","+limit2+";";
			ResultSet rs0 = dao.executeQuery(sql0);
			String sql1="SELECT count(*) FROM aivonrrecord2 where rowId ='total' order by dataid+0;";
			ResultSet rs1 = dao.executeQuery(sql1);
			try {
				jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
				if(rs1.next()) {
					count = rs1.getInt(1);
				}
			} catch (JSONException | SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}		
			
		}else {
			String sql0="SELECT * FROM aivonrrecord2 where rowId ='total' and CONCAT(dataid,status,rowId,filename,result,lossInfo,start,end) like  '%"+id+"%' order by dataid+0 desc limit "+limit1+","+limit2+";";
			ResultSet rs0 = dao.executeQuery(sql0);
			String sql1="SELECT count(*) FROM aivonrrecord2 where rowId ='total' and CONCAT(dataid,status,rowId,filename,result,lossInfo,start,end) like  '%"+id+"%' order by dataid+0;";
			ResultSet rs1 = dao.executeQuery(sql1);
			try {
				jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
				if(rs1.next()) {
					count = rs1.getInt(1);
				}
			} catch (JSONException | SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}	
			
		}
		
		
		for(int i=0;i<jsonArray0.length();i++) {
			JSONObject object = jsonArray0.getJSONObject(i);
			String dataid = object.getString("dataid");
			String sql = "SELECT * FROM aivonrrecord2 where rowId !='total' and dataid='"+dataid+"' order by rowId+0";
			ResultSet rs = dao.executeQuery(sql);
			JSONArray jsonArray1= new JSONArray();
			try {
				jsonArray1 = ResultSetToJsonArray.resultSetToJsonArray(rs);
			} catch (JSONException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			object.put("subData", jsonArray1);
		}
		JSONObject object = new JSONObject();
		object.put("code", 0);
		object.put("msg","");
		object.put("count", count);
		object.put("data", jsonArray0);
//		System.out.println(object.toString());
//		result.put(ptype);
//        result.put(jsonArray0);
//        result.put(count);
		dao.close();
		PrintWriter out = response.getWriter(); 
		out.print(object);
		out.flush();
		out.close();
	}

	public static String getRadomFileName(){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		Date date = new Date();
		String str = simpleDateFormat.format(date);
	
		Random random = new Random();
		int rannum = (int)(random.nextDouble()*(99999999-10000000+1))+10000000;
	
		return rannum+str;
	}


	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
}




