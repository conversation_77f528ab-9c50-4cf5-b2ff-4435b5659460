var xmlHttp=false;  
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}
function init(gnbids,pcis){
	gnbids=encodeURIComponent(gnbids);
    pcis=encodeURIComponent(pcis);
	createXMLHttpRequest();
	xmlHttp.onreadystatechange = initcallBack;
	var url=encodeURI("ParamqueryinfoServlet?gnbid="+gnbids+"&pci="+pcis);
	xmlHttp.open("POST", url, true);
	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
	xmlHttp.send(null);	
}
function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			table(data);
		}
	}	
}

function table(data){
	var tbody1  = document.getElementById("infotbody1") ; 
	s="";
	for(var i=0;i<data.length;i++){
		if(i%2==0){
			s+="<tr bgcolor='#ECF5FF'>"
		}else{
			s+="<tr bgcolor='#FBFBFF'>"
		}
		s+="<td style='text-align:center'>"+(i+1)+"</td>";
		s+="<td style='text-align:center'>"+data[i][0]+"</td>";
		s+="<td style='text-align:center'>"+data[i][1]+"</td>";
		s+="<td style='text-align:center'>"+data[i][2]+"</td>";
		s+="<td style='text-align:center'>"+data[i][3]+"</td>";
		s+="<td style='text-align:center'>"+data[i][4]+"</td>";
		s+="<td style='text-align:center'>"
		for(var j=0;j<data[i][5].length;j++){
			s+=data[i][5][j]+'<br>'
		}
		s+="</td>";
		if(data[i][5].length==0){
			s+="<td style='text-align:center'>"+"<input id='"+(i+1)+"' value='"+(i+1)+"' type='checkbox'>"+"</td>"
		}else{
			s+="<td style='text-align:center'>"+"<input id='"+(i+1)+"' value='"+(i+1)+"' type='checkbox' checked='true'>"+"</td>"
		}
		s+="<td style='text-align:center'>"+data[i][6]+"</td>";
		s+="</tr>"
	}
	tbody1.innerHTML=s;
}

function check(){
	var t = document.getElementById( "infotbody1" );
	var rows = t.rows;
	var gnbids="";
	var pcis="";
	var aautypes="";
	for(var i=0;i<rows.length;i++){
		ischeck = document.getElementById((i+1)).checked;	
		if(ischeck==false) continue;
		for(var j=0;j<rows[i].cells.length;j++){
			if(j==1){
					gnbids = gnbids+rows[i].cells[j].innerText+"#";
			}else if(j==2){
					pcis = pcis+rows[i].cells[j].innerText+"#";
			}else if(j==5){
					aautypes = aautypes+rows[i].cells[j].innerText+"#";
			}
		}
	}
	if(gnbids.length==0){
		alert("您未选择，请核查")
	}else{
		gnbids = gnbids.substring(0,gnbids.length-1);
		pcis = pcis.substring(0,pcis.length-1);
		aautypes = aautypes.substring(0,aautypes.length-1);
		gnbids=encodeURIComponent(gnbids);
    	pcis=encodeURIComponent(pcis);
    	aautypes=encodeURIComponent(aautypes);
    	
    	document.getElementById("loading").style.display = 'block';
    	
		createXMLHttpRequest();
		xmlHttp.onreadystatechange = checkcallBack;
		var url=encodeURI("ParamcheckServlet?gnbids="+gnbids+"&pcis="+pcis+"&aautypes="+aautypes);
		xmlHttp.open("POST", url, true);
		xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
		xmlHttp.send(null);	
	}

}
function checkcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			
			document.getElementById("loading").style.display = 'none';
			document.getElementById("result1").style.display = 'block';
			document.getElementById("result2").style.display = 'none';
			resulttable1(data)
		}
	}	
}

function resulttable1(data){
	var tbody1  = document.getElementById("resultbody1") ; 
	s="";
	for(var i=0;i<data.length;i++){
		if(i%2==0){
			s+="<tr bgcolor='#ECF5FF'>"
		}else{
			s+="<tr bgcolor='#FBFBFF'>"
		}
		strings = data[i][1].split('#')
		s+="<td style='text-align:center'>"+strings[0]+"</td>";
		s+="<td style='text-align:center'>"+strings[1]+"</td>";
		s+="<td style='text-align:center'>"+data[i][0]+"</td>";
		s+="<td style='text-align:center'>"+"<button style='margin-top: 10px;' class='layui-btn layui-btn-normal layui-btn-radius' lay-submit lay-filter='broadcast' onclick='downloadlog()'>下载</button>" 
        s+="</td>";
		s+="</tr>"
	}
	tbody1.innerHTML=s;
}

function config(){
	var t = document.getElementById( "infotbody1" );
	var rows = t.rows;
	var gnbids="";
	var pcis="";
	var aautypes="";
	for(var i=0;i<rows.length;i++){
		ischeck = document.getElementById((i+1)).checked;	
		if(ischeck==false) continue;
		for(var j=0;j<rows[i].cells.length;j++){
			if(j==1){
					gnbids = gnbids+rows[i].cells[j].innerText+"#";
			}else if(j==2){
					pcis = pcis+rows[i].cells[j].innerText+"#";
			}else if(j==5){
					aautypes = aautypes+rows[i].cells[j].innerText+"#";
			}
		}
	}
	if(gnbids.length==0){
		alert("您未选择，请核查")
	}else{
		gnbids = gnbids.substring(0,gnbids.length-1);
		pcis = pcis.substring(0,pcis.length-1);
		aautypes = aautypes.substring(0,aautypes.length-1);
		gnbids=encodeURIComponent(gnbids);
    	pcis=encodeURIComponent(pcis);
    	aautypes=encodeURIComponent(aautypes);
    	
    	document.getElementById("loading").style.display = 'block';

		createXMLHttpRequest();
		xmlHttp.onreadystatechange = configcallBack;
		var url=encodeURI("ParamconfigServlet?gnbids="+gnbids+"&pcis="+pcis+"&aautypes="+aautypes);
		xmlHttp.open("POST", url, true);
		xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
		xmlHttp.send(null);	
	}

}
function configcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			
			document.getElementById("loading").style.display = 'none';
			document.getElementById("result1").style.display = 'none';
			document.getElementById("result2").style.display = 'block';
			resulttable2(data)
		}
	}	
}

function resulttable2(data){
	var tbody1  = document.getElementById("resultbody2") ; 
	s="";
	for(var i=0;i<data.length;i++){
		if(i%2==0){
			s+="<tr bgcolor='#ECF5FF'>"
		}else{
			s+="<tr bgcolor='#FBFBFF'>"
		}
		strings = data[i][1].split('#')
		s+="<td style='text-align:center'>"+strings[0]+"</td>";
		s+="<td style='text-align:center'>"+strings[1]+"</td>";
		s+="<td style='text-align:center'>"+data[i][0]+"</td>";
		s+="<td style='text-align:center'>"+"<button style='margin-top: 10px;' class='layui-btn layui-btn-normal layui-btn-radius' lay-submit lay-filter='broadcast' onclick='downloadlog()'>下载</button>" 
        s+="</td>";
        s+="<td style='text-align:center'>"+data[i][2]+"</td>";
		s+="<td style='text-align:center'>"+"<button style='margin-top: 10px;' class='layui-btn layui-btn-normal layui-btn-radius' lay-submit lay-filter='broadcast' onclick='active()'>激活</button>" 
		s+="</tr>"
	}
	tbody1.innerHTML=s;
}


function downloadlog(){
	var td = event.srcElement;
	tr =td.parentElement.parentElement; 
	var umeip=tr.cells[0].innerText;
	var logpath=tr.cells[2].innerText;
	window.location.href='/XJGnbMonitorPanel/Paramdownloadlog?logpath='+logpath+"&umeip="+umeip
}

function active(){
	var str = prompt('请输入指令');
	if(str=="asd"){
		var td = event.srcElement;
		tr =td.parentElement.parentElement; 
		var umeip=tr.cells[0].innerText;
		var id=tr.cells[4].innerText;
		createXMLHttpRequest();
		xmlHttp.onreadystatechange = activecallBack;
		var url=encodeURI("ParamactivePlanning?id="+id+"&umeip="+umeip);
		xmlHttp.open("POST", url, true);
		xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
		xmlHttp.send(null);	
	}
	else{
		alert('指令错误');
	}
}

function activecallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			alert("已激活")
		}
	}	
}

