function sleep(ms){
	let start = Date.now();
	let end = start + ms;
	while(true){
		if(Date.now()> end){
			return
		}
	}
}

var xmlHttp = false;
function createXMLHttpRequest() {
	if (window.ActiveXObject) {
		xmlHttp = new ActiveXObject("Microsoft.XMLHTTP");
	}
	else if (window.XMLHttpRequest) {
		xmlHttp = new XMLHttpRequest();
	}
}

function up(){
	var gnbid  =  document.getElementById( "gnbid" ).value; 
	var version  =  document.getElementById( "version" ).value; 
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = upcallBack; 
    var url=encodeURI("GnbidupgradeServlet?gnbid="+gnbid+"&version="+version);

    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}

function upcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);	
			alert("已创建升级任务请查看");
		}
	}
}