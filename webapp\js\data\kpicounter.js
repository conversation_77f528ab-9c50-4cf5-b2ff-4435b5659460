var xmlHttp=false;  

 function createXMLHttpRequest() {
     if (window.ActiveXObject){
           xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
     }
     else if (window.XMLHttpRequest){
           xmlHttp = new XMLHttpRequest();
      }  
 }

function init(index,dev)
{
//	var count=null; var s  =  document.getElementById( "count1" ); 
//	count=s.options[s.selectedIndex].text;
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = initcallBack;
    var url="Query1CIdevkpiInfoServlet?index="+index+"&dev="+dev;  
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);
}

function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
			var char = document.getElementById("chart1");
			chart(char,data[0],data[1]);
        }
     }
}

function chart(divname,data,data2){
	var xx=[];xx.push(0);
	for(var i=1;i<data.length;i++){
		if(data[i]!=data[i-1]){
			xx.push(i)
		}
	}
	var index=[];
	for(var i=0;i<xx.length;i++){
		if(i==0){
//			index.push(Math.ceil(xx[i]/2));
		}else{
			index.push( Math.floor((xx[i]-xx[i-1])/2 + xx[i-1]));
		}
	}
	index.push(Math.ceil((data.length-1-xx[xx.length-1])/2 + xx[xx.length-1]))
	var chart =new Highcharts.Chart(divname, {
		chart: {
//				zoomType: 'x',
		},
		title: {
				text: '',
		},
		credits:{
			enabled:false,
		},
		scrollbar:{
			enabled:true,
			height:15,
		    barBackgroundColor: "#d1d1d1",
		    barBorderColor: "#d1d1d1",
		    rifleColor: "#d1d1d1",
		    barBorderRadius: 5,
		    buttonBorderWidth: 0,
		    buttonArrowColor: "rgba(0,0,0,0)",
		},
	    xAxis: [
		{
			tickWidth:1,
			categories:data, 
			tickPositions:index,
			labels: {
				rotation:-30,
					style: {
						
					}
			},
			max:data2.length-1,
			min:data2.length-1-30
		}],

		yAxis: [{ // Primary yAxis
				labels: {
						format: '{value}',
						style: {
								color: Highcharts.getOptions().colors[0]
						}
				},
				title: {
						text: '数值',
						style: {
								color: Highcharts.getOptions().colors[0]
						}
				}
				
		}],
		 tooltip: {
				pointFormat:'{point.name}  <b>{point.y}</b> '
		},
		plotOptions: {
        	spline: {
            dataLabels: {
                // 开启数据标签
                enabled: true  ,
//                formatter:function(){
//	return this.point.name+Highcharts.numberFormat(this.percentage,2)+'%';
//				},
				format:"{point.y:.4f}"     
            }
            // 关闭鼠标跟踪，对应的提示框、点击事件会失效
//            enableMouseTracking: false
        }
    },
	series: 
	[ {
			name: '数值',
			type: 'spline',
			data: data2,
			tooltip: {
					valueSuffix: ' '
			},findNearestPointBy:'xy'
	}]
});
	
}


