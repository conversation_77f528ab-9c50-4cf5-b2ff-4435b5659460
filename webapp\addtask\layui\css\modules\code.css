/**
 * code
 */

/* 加载就绪标志 */
html #layuicss-skincodecss{display:none; position: absolute; width:1989px;}

/* 默认风格 */
.layui-code-view{display: block; position: relative; margin: 10px 0; padding: 0; border: 1px solid #eee; border-left-width: 6px; background-color: #FAFAFA; color: #333; font-family: Courier New; font-size: 13px;}
.layui-code-title{position: relative; padding: 0 10px; height: 40px; line-height: 40px; border-bottom: 1px solid #eee; font-size: 12px;}
.layui-code-title > .layui-code-about{position: absolute; right: 10px; top: 0; color: #B7B7B7;}
.layui-code-about > a{padding-left: 10px;}
.layui-code-view > .layui-code-ol, 
.layui-code-view > .layui-code-ul{position: relative; overflow: auto;}
.layui-code-view > .layui-code-ol > li{position: relative; margin-left: 45px; line-height: 20px; padding: 0 10px; border-left: 1px solid #e2e2e2; list-style-type: decimal-leading-zero; *list-style-type: decimal; background-color: #fff;}
.layui-code-view > .layui-code-ol > li:first-child, 
.layui-code-view > .layui-code-ul > li:first-child{padding-top: 10px;}
.layui-code-view > .layui-code-ol > li:last-child, 
.layui-code-view > .layui-code-ul > li:last-child{padding-bottom: 10px;}
.layui-code-view > .layui-code-ul > li{position: relative; line-height: 20px; padding: 0 10px; list-style-type: none; *list-style-type: none; background-color: #fff;}
.layui-code-view pre{margin: 0;}

/* 深色风格 */
.layui-code-dark{border: 1px solid #0C0C0C; border-left-color: #3F3F3F; background-color: #0C0C0C; color: #C2BE9E}
.layui-code-dark > .layui-code-title{border-bottom: none;}
.layui-code-dark > .layui-code-ol > li, 
.layui-code-dark > .layui-code-ul > li{background-color: #3F3F3F; border-left: none;}
.layui-code-dark > .layui-code-ul > li{margin-left: 6px;}

/* 代码预览 */
.layui-code-demo .layui-code{visibility: visible !important; margin: -15px; border-top: none; border-right: none; border-bottom: none;}
.layui-code-demo .layui-tab-content{padding: 15px; border-top: none}