<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>语音分析详情 - iWork自动化测试</title>
<meta name="author" content="xuejie10170365">
<link href="css/bootstrap.min.css" rel="stylesheet">
<link href="css/materialdesignicons.min.css" rel="stylesheet">
<link href="css/style.min.css" rel="stylesheet">
<link href="addtask/layui-v2.8.3/layui/css/layui.css" type="text/css" rel="stylesheet">
<link href="css/layer/layer.css" type="text/css" rel="stylesheet">

</head>
<style>
.detail-card {
    margin-bottom: 20px;
}
.detail-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    border-left: 4px solid #1E9FFF;
    padding-left: 10px;
}
.info-item {
    margin-bottom: 10px;
}
.info-label {
    font-weight: bold;
    color: #666;
    display: inline-block;
    width: 120px;
}
.info-value {
    color: #333;
}
.status-success {
    color: #5FB878;
}
.status-fail {
    color: #FF5722;
}
.back-btn {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
}
</style>
<body>
<div class="container-fluid p-t-15">
    <!-- 返回按钮 -->
    <button type="button" class="layui-btn layui-btn-sm layui-btn-radius back-btn" onclick="window.close()">
        <i class="layui-icon layui-icon-return"></i> 返回
    </button>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <center style="padding-top: 20px">
                    <a style="font-size: 24px;font-weight: 500;">语音分析详情<span class="layui-badge layui-bg-blue">iWork</span></a>
                </center>
                
                <div class="card-body" style="padding: 30px;">
                    <!-- 基本信息 -->
                    <div class="detail-card">
                        <div class="detail-title">基本信息</div>
                        <div class="layui-row">
                            <div class="layui-col-md6">
                                <div class="info-item">
                                    <span class="info-label">数据ID：</span>
                                    <span class="info-value" id="dataid">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">文件名：</span>
                                    <span class="info-value" id="filename">-</span>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="info-item">
                                    <span class="info-label">文件路径：</span>
                                    <span class="info-value" id="filepath">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">查询时间：</span>
                                    <span class="info-value" id="querytime">-</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分析结果表格 -->
                    <div class="detail-card">
                        <div class="detail-title">分析结果</div>
                        <table id="detailTable" lay-filter="detailTable" class="layui-table">
                        </table>
                    </div>

                    <!-- API数据 -->
                    <div class="detail-card" id="apiDataCard" style="display: none;">
                        <div class="detail-title">远程API数据</div>
                        <div class="layui-elem-quote">
                            <pre id="apiDataContent" style="background: #f8f8f8; padding: 15px; border-radius: 4px; max-height: 400px; overflow-y: auto;"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/main.min.js"></script>
<script type="text/javascript" src="addtask/layui-v2.8.3/layui/layui.js"></script>

<script>
layui.use(['table', 'layer'], function(){
    var table = layui.table;
    var layer = layui.layer;
    
    // 获取URL参数
    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return decodeURIComponent(r[2]);
        return null;
    }
    
    // 获取参数
    var dataid = getUrlParam('dataid');
    var filename = getUrlParam('filename');
    var filepath = getUrlParam('path');
    
    // 显示基本信息
    document.getElementById('dataid').textContent = dataid || '-';
    document.getElementById('filename').textContent = filename || '-';
    document.getElementById('filepath').textContent = filepath || '-';
    document.getElementById('querytime').textContent = new Date().toLocaleString();
    
    // 查询详细数据
    if (dataid) {
        layer.load();
        
        $.ajax({
            url: 'QueryVonrDetailServlet',
            type: 'POST',
            data: {
                dataid: dataid,
                filename: filename,
                path: filepath
            },
            dataType: 'json',
            success: function(res) {
                layer.closeAll('loading');
                
                if (res.code === 0) {
                    // 渲染表格
                    if (res.localData && res.localData.length > 0) {
                        table.render({
                            elem: '#detailTable',
                            data: res.localData,
                            cols: [[
                                { field: 'dataid', title: '任务ID', width: 100 },
                                { field: 'rowId', title: '子任务ID', width: 100 },
                                { field: 'status', title: '状态', width: 120, templet: function (d) {
                                    if (d.status === "success") {
                                        return '<span class="status-success">' + d.status + '</span>';
                                    } else if (d.status === "fail") {
                                        return '<span class="status-fail">' + d.status + '</span>';
                                    }
                                    return d.status;
                                } },
                                { field: 'start', title: '开始时间', width: 180 },
                                { field: 'end', title: '结束时间', width: 180 },
                                { field: 'filename', title: '文件名', width: 200 },
                                { field: 'result', title: '结果', width: 150 },
                                { field: 'lossInfo', title: '详情', minWidth: 200 }
                            ]],
                            page: false
                        });
                    } else {
                        document.getElementById('detailTable').innerHTML = '<div style="text-align: center; padding: 50px; color: #999;">暂无分析结果数据</div>';
                    }
                    
                    // 显示API数据
                    if (res.hasApiData && res.apiData) {
                        document.getElementById('apiDataCard').style.display = 'block';
                        document.getElementById('apiDataContent').textContent = JSON.stringify(res.apiData, null, 2);
                    }
                } else {
                    layer.msg('查询失败：' + res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.closeAll('loading');
                layer.msg('查询请求异常', {icon: 2});
            }
        });
    } else {
        layer.msg('缺少必要参数', {icon: 2});
    }
});
</script>

</body>
</html>
