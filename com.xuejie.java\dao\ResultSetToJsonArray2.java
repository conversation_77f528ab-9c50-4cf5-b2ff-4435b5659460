package dao;
import java.sql.ResultSet;
import java.sql.SQLException;

import com.mysql.cj.jdbc.result.ResultSetMetaData;

import net.sf.json.JSONArray;
import net.sf.json.JSONException;
import net.sf.json.JSONObject;




public class ResultSetToJsonArray2 {
	
public static JSONArray resultSetToJsonArray(ResultSet rs) throws SQLException,JSONException   
	
	{   
	
	   JSONArray array = new JSONArray();         
	   // 閿熸枻鎷峰彇閿熸枻鎷烽敓鏂ゆ嫹   
	   ResultSetMetaData metaData = (ResultSetMetaData) rs.getMetaData();   
	   int columnCount = metaData.getColumnCount();          
	   // 閿熸枻鎷烽敓鏂ゆ嫹ResultSet閿熷彨纰夋嫹姣忛敓鏂ゆ嫹閿熸枻鎷烽敓锟�   
	    while (rs.next()) {   
	        JSONObject jsonObj = new JSONObject(); 
	//        JSONObject jsonObj = JSONObject.fromObject(json);           
	        // 閿熸枻鎷烽敓鏂ゆ嫹姣忎竴閿熸枻鎷�   
	        for (int i = 1; i <= columnCount; i++) { 
	            String columnName =metaData.getColumnLabel(i); 
	            String value = rs.getString(columnName);  
	            jsonObj.put(columnName, value);   
	        }    
	        array.add(jsonObj);  
	    }     
	   return array;   
	}  
	

}
