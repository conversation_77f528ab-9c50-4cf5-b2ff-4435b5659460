var xmlHttp=false;  
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}
function getdata(gnbid)
{
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = gnbidinfocallBack;
    var url=encodeURI("QueryGnbInfoServlet?gnbid="+gnbid);  url=encodeURI(url);
    xmlHttp.open("GET", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);
}

function gnbidinfocallBack(){
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data=JSON.parse(data);
			fwtable(data[0]);
			fwlinetable(data[1]);
			swtable(data[2]);
			celltable(data[3])
			var length = data[4].length;
//			alert(length);
			alarmnum(data[4][length-1]);
			alarmtable(data[4]);
		}
	}
}



function submit(gnbid)
{
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = submitcallBack;
    var rruNo  =  document.getElementById( "rruNo" ).value; 
    var pci  =  document.getElementById( "pci" ).value; 
    var cellid  =  document.getElementById( "cellid" ).value; 
    var band  =  document.getElementById( "band" ).value; 
    rruNo = encodeURIComponent(rruNo);
    pci = encodeURIComponent(pci);
    cellid = encodeURIComponent(cellid);
    band = encodeURIComponent(band);
    var url=encodeURI("CreateConfigFileServlet?rruNo="+rruNo+"&pci="+pci+"&gnbid="+gnbid+"&band="+band+"&cellid="+cellid);  
//    url=encodeURI(url);
    xmlHttp.open("GET", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);  	
}

function submitcallBack(){
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data=JSON.parse(data);
			alert("已生成");
			fwtable(data[0]);
			fwlinetable(data[1]);
			swtable(data[2]);
			celltable(data[3])
			var length = data[4].length;
			alarmnum(data[4][length-1]);
			alarmtable(data[4]);
			
		}
	}
}

function fwtable(data){
//	var fwtable  =  document.getElementById( "fwtable" ); 
//	var s="<thead><th>对象标识</th><th>槽位编号</th><th>设备名称</th><th>硬件场景</th><th>设备功能模式</th><th>硬件功能</th><th>运行状态</th></thead>";
//	s+="<tbody>";
	for(var i=0;i<data.length;i++){	
		
		if(data[i].slotNo!="1"&&data[i].slotNo!="2"&&data[i].slotNo!="3"&&data[i].slotNo!="4"&&data[i].slotNo!="5"
		&&data[i].slotNo!="6"&&data[i].slotNo!="7"&&data[i].slotNo!="8"&&data[i].slotNo!="14") {
			continue;
		}
		var div = "s" +data[i].slotNo;
		if(div!="s"){
			document.getElementById( div ).innerHTML = data[i].name;
			document.getElementById( div ).style.background= "rgba(14, 148, 234, 0.4)";
			if(data[i].operState == "故障"){
				document.getElementById( div ).style.color="#FF0000";
			}
		}
	}
//		s+="<tr>";
//		s+="<td>"+data[i].moId+"</td>";
//		s+="<td>"+data[i].slotNo+"</td>";
//		s+="<td>"+data[i].name+"</td>";
//		s+="<td>"+data[i].hwWorkScence+"</td>";
//		s+="<td>"+data[i].functionMode+"</td>";
//		s+="<td>"+data[i].hwFunction+"</td>";
//		s+="<td>"+data[i].operState+"</td>";
//		s+="</tr>"
//	}
//	s+="</tbody>"
//	fwtable.innerHTML=s;	
}

function fwlinetable(data){
	var fwlinetable  =  document.getElementById( "fwlinetable" ); 
	var s="<thead><th>基带板位置</th><th>基带板名称</th><th>光口位置</th><th>RRU编号</th><th>RRU名称</th><th>光口位置</th></thead>";
	s+="<tbody>";
	for(var i=0;i<data.length;i++){
//		if(data[i].operState=="正常"){
			s+="<tr>";
			if(data[i].vbpoperState=="故障"){
				s+="<td><span style='color: #FF0000;'>" +data[i].vbpslot+"</span></td>";
				s+="<td><span style='color: #FF0000;'>"+data[i].vbp+"</span></td>";
				s+="<td><span style='color: #FF0000;'>"+data[i].vbplocate+"</span></td>";
			}else{ 
				s+="<td>"+data[i].vbpslot+"</td>";
				s+="<td>"+data[i].vbp+"</td>";
				s+="<td>"+data[i].vbplocate+"</td>";
			}
			if(data[i].rruoperState=="故障"){
				s+="<td><span style='color: #FF0000;'>"+data[i].rruNo+"</span></td>";
				s+="<td><span style='color: #FF0000;'>"+data[i].rru+"</span></td>";
				s+="<td><span style='color: #FF0000;'>"+data[i].rrulocate+"</span></td>";
			}else if(data[i].rruoperState=="正常"){
				s+="<td>"+data[i].rruNo+"</td>";
				s+="<td>"+data[i].rru+"</td>";
				s+="<td>"+data[i].rrulocate+"</td>";
			}
			
			
			
			s+="</tr>"
//		}
	}
	s+="</tbody>"
	fwlinetable.innerHTML=s;	
}

function swtable(data){
	var swtable  =  document.getElementById( "swtable" ); 
	var s="<thead><th>网元类型</th><th>网络制式</th><th>子网</th><th>版本包</th><th>版本包状态</th><th>IP</th></thead>";
	s+="<tbody>";
	for(var i=0;i<data.length;i++){
		s+="<tr>";
		s+="<td>"+data[i].netype+"</td>";
		s+="<td>"+data[i].producttype+"</td>";
		s+="<td>"+data[i].subnetwork+"</td>";
		s+="<td>"+data[i].version+"</td>";
		s+="<td>"+data[i].swstatus+"</td>";
		s+="<td>"+data[i].ip+"</td>";
		s+="</tr>"
	}
	s+="</tbody>"
	$('#swtable').html(s);
//	swtable.innerHTML=s;	
}

function celltable(data){
	var celltable  =  document.getElementById( "celltable" ); 
	var s="<thead><th>PCI</th><th>频点</th><th>管理状态</th><th>小区节能状态</th><th>小区服务状态</th></thead>";
	s+="<tbody>";
	for(var i=0;i<data.length;i++){
		if(data[i].serviceStatus=="退服"){
			s+="<tr>";
			s+="<td><span style='color: #FF0000;'>"+data[i].pci+"</span></td>";
			s+="<td><span style='color: #FF0000;'>"+data[i].ssbFrequency+"</span></td>";
			s+="<td><span style='color: #FF0000;'>"+data[i].adminState+"</span></td>";
			s+="<td><span style='color: #FF0000;'>"+data[i].cellEsState+"</span></td>";
			s+="<td><span style='color: #FF0000;'>"+data[i].serviceStatus+"</span></td>";
			s+="</tr>"
		}else{
			s+="<tr>";
			s+="<td>"+data[i].pci+"</td>";
			s+="<td>"+data[i].ssbFrequency+"</td>";
			s+="<td>"+data[i].adminState+"</td>";
			s+="<td>"+data[i].cellEsState+"</td>";
			s+="<td>"+data[i].serviceStatus+"</td>";
			s+="</tr>"
		}
		
		
	}
	s+="</tbody>"
	$('#celltable').html(s);
//	swtable.innerHTML=s;	
}
function alarmnum(data){

	var chart = Highcharts.chart('alarmnumchart', {
	chart: {
		polar: true,
		type: 'line',
		backgroundColor: '#06182d'
	},
	title: {
		text: '',
		x: -80
	},
	pane: {
		size: '80%'
	},
	xAxis: {
		categories: ['严重', '主要', '次要', '警告'],
		tickmarkPlacement: 'on',
		lineWidth: 0,
		labels: {
				style: { color: 'white' },
				rotation: 0  // 设置轴标签旋转角度
			}
	},
	yAxis: {
		gridLineInterpolation: 'polygon',
		lineWidth: 0,
		min: 0
	},
	tooltip: {
		shared: true,
		pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.0f}个</b><br/>'
	},
	legend: {
		align: 'right',
		enabled:false,
		verticalAlign: 'top',
		y: 70,
		layout: 'vertical'
	},
	series: [{
		name: '告警',
		data: [data.Critical, data.Major, data.Minor, data.Warning],
		pointPlacement: 'on',
		dataLabels: {
				enabled: true,
				rotation: -90,
				color: '#FFFFFF',
				align: 'right',
				format: '{point.y}', // :.1f 为保留 1 位小数
				y: 10
			}
	}]
});
}


function alarmtable(data){
	var alarmtable  =  document.getElementById( "alarmtable" ); 
	var s="<thead><th>告警级别</th><th>位置</th><th>告警码名称</th></thead>";
	s+="<tbody>";
	for(var i=0;i<data.length-1;i++){
		s+="<tr>";
		s+="<td>"+data[i].perceivedseverityname+"</td>";
		s+="<td>"+data[i].positionname+"</td>";
		s+="<td>"+data[i].codename+"</td>";
		s+="</tr>"
	}
	s+="</tbody>"
	alarmtable.innerHTML=s;	
}



