# vonr3.jsp 和 vonrinfo3.js 功能说明

## 概述
基于 vonr2.jsp 和 vonrinfo2.js 复制生成的 vonr3.jsp 和 vonrinfo3.js，参考 vonr4.jsp 的第二个 tab 页实现了语音 log 上传功能。

## 主要功能

### 1. 语音分析记录（第一个Tab）
- 显示语音分析任务记录
- 支持搜索功能
- 支持查看详情和在线播放

### 2. 语音Log上传（第二个Tab）
- **WNG文件上传**：支持 .apm、.dlf、.saf 格式文件
- **Audio文件上传**：支持音频、视频格式文件
- **批量上传**：可同时选择多个WNG文件和Audio文件
- **上传进度显示**：显示上传进度条
- **结果记录**：显示上传结果和任务ID

## 技术实现

### 前端技术栈
- **Layui框架**：用于UI组件和表格渲染
- **jQuery**：用于DOM操作和AJAX请求
- **Bootstrap**：用于响应式布局

### 后端接口
- **ApiUploadServlet**：处理文件上传请求
  - 接收WNG文件和Audio文件
  - 调用外部API进行处理
  - 将结果保存到数据库

### 数据库表
- **aivonrlog2**：存储上传记录
  - userid：用户ID
  - uploadtime：上传时间
  - status：状态
  - taskid：任务ID
  - filename：文件信息

## 文件结构

```
webapp/
├── vonr3.jsp                 # 主页面文件
├── js/vonrinfo3.js          # JavaScript逻辑文件
└── vonr3_readme.md          # 说明文档

com.xuejie.java/temp/
└── ApiUploadServlet.java    # 后端上传处理Servlet
```

## 使用方法

### 1. 访问页面
访问 `vonr3.jsp` 页面

### 2. 上传文件
1. 点击第二个Tab "语音Log上传"
2. 选择WNG文件（.apm、.dlf、.saf格式）
3. 选择Audio文件（音频、视频格式）
4. 点击"开始上传"按钮
5. 确认上传信息
6. 等待上传完成

### 3. 查看结果
- 上传完成后会显示任务ID
- 在"上传结果记录"表格中查看上传历史
- 点击"查询"按钮查看分析报告

## 主要特性

### 1. 文件验证
- 支持多种文件格式
- 文件大小限制（最大2000MB）
- 文件数量统计显示
- **文件名格式校验**：
  - WNG文件：以"-"分割，第三个是VONR/VOLTE，第四个是主叫/被叫
  - Audio文件：以"_"分割，第二个是电话号码(11位数字)，第三个是时间(14位数字)

### 2. 用户体验
- 响应式设计，支持移动端
- 文件选择状态实时反馈
- 上传进度可视化
- 错误信息友好提示

### 3. 安全性
- 用户身份验证（通过cookie获取登录信息）
- 文件类型验证
- 请求超时控制

## 文件名校验规则

### WNG文件命名规则
- **分割符**：使用"-"（横线）分割
- **最少部分**：至少4个部分
- **第三部分**：必须是"VONR"或"VOLTE"（不区分大小写）
- **第四部分**：必须是"主叫"或"被叫"
- **示例**：
  - ✅ 正确：`AI智能打卡测试-广州中兴-VONR-主叫.apm`
  - ✅ 正确：`测试项目-北京联通-VOLTE-被叫.dlf`
  - ❌ 错误：`测试-广州-VoNR-主叫.apm`（第三部分不是VONR/VOLTE）
  - ❌ 错误：`测试-广州-VONR-呼叫.apm`（第四部分不是主叫/被叫）

### Audio文件命名规则
- **分割符**：使用"_"（下划线）分割
- **最少部分**：至少3个部分
- **第二部分**：必须是11位数字（电话号码）
- **第三部分**：必须是14位数字（时间格式：YYYYMMDDHHMMSS）
- **时间验证**：年份2020-2030，月份1-12，日期1-31，小时0-23，分钟0-59，秒0-59
- **示例**：
  - ✅ 正确：`广州中兴_17344224465_20250508110536.m4a`
  - ✅ 正确：`北京测试_13812345678_20241201143022.wav`
  - ❌ 错误：`广州中兴_1734422446_20250508110536.m4a`（电话号码不是11位）
  - ❌ 错误：`广州中兴_17344224465_2025050811053.m4a`（时间不是14位）
  - ❌ 错误：`广州中兴_17344224465_20250532110536.m4a`（日期32无效）

### 校验时机
1. **文件选择时**：实时校验，显示错误文件列表
2. **上传前**：再次校验，阻止格式错误的文件上传
3. **错误提示**：详细显示错误文件名和格式要求

## 配置说明

### 1. API配置
在 `ApiUploadServlet.java` 中配置：
```java
private static final String API_URL = "http://10.231.145.183:10888/api/report/upload/files";
```

### 2. 文件大小限制
```java
private static final int MAX_FILE_SIZE = 1024 * 1024 * 2000; // 2000MB
private static final int MAX_REQUEST_SIZE = 1024 * 1024 * 4000; // 4000MB
```

### 3. 超时设置
```java
private static final long DEFAULT_TIMEOUT_SECONDS = 300; // 5分钟
```

## 注意事项

1. **文件命名规范**：
   - **WNG文件格式**：以"-"分割，第三个是VONR/VOLTE，第四个是主叫/被叫
     - 示例：AI智能打卡测试-广州中兴-VONR-主叫.apm
     - 验证规则：至少4个部分，第3部分必须是VONR或VOLTE，第4部分必须是主叫或被叫
   - **Audio文件格式**：以"_"分割，第二个是电话号码(11位)，第三个是时间(14位)
     - 示例：广州中兴_17344224465_20250508110536.m4a
     - 验证规则：至少3个部分，第2部分必须是11位数字，第3部分必须是14位时间格式(YYYYMMDDHHMMSS)

2. **网络要求**：
   - 需要能够访问外部API接口
   - 建议在稳定的网络环境下使用

3. **浏览器兼容性**：
   - 支持现代浏览器（Chrome、Firefox、Safari、Edge）
   - 需要启用JavaScript

## 错误处理

### 常见错误及解决方案

1. **上传失败**：
   - 检查网络连接
   - 确认文件格式正确
   - 检查文件大小是否超限

2. **查询失败**：
   - 确认任务ID正确
   - 检查后端服务状态

3. **页面加载异常**：
   - 清除浏览器缓存
   - 检查JavaScript控制台错误信息

## 更新日志

### v1.0 (当前版本)
- 基于vonr2.jsp和vonrinfo2.js创建
- 参考vonr4.jsp第二个tab页实现语音log上传功能
- 支持WNG和Audio文件批量上传
- 集成ApiUploadServlet后端处理
- 实现上传结果记录和查询功能
