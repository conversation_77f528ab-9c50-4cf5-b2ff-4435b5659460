var xmlHttp=false;  
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}
function test(){
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = testcallBack;
    	var url="ParamcheckServlettest";  
    	xmlHttp.open("GET", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);	
}
function testcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			if(data[0]==""){
				alert("任务执行失败");
			}else{
				document.getElementById("loginfo").value = data[0]
			}
		}
	}	
}

function downloadlog(){
	logpath=document.getElementById("loginfo").value;
	alert(logpath);
	window.location.href='/XJGnbMonitorPanel/Paramdownloadlog?logpath='+logpath
}