var xmlHttp=false;  

 function createXMLHttpRequest() {
     if (window.ActiveXObject){
           xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
     }
     else if (window.XMLHttpRequest){
           xmlHttp = new XMLHttpRequest();
      }  
 }

var v;  
var data;  
function init(dev) 
{	
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = initcallBack;
    var url="Query1CIdevInfoServlet?dev="+dev;  
    v=dev;
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}

function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
			var s  =  document.getElementById( "version1" ); 
        	for(var i=0;i<data[0].length;i++){
        	   var option  =   new  Option(data[0][i],i);   
        	   s.options[i] = option;  	   	 	
			} 
			var s2  =  document.getElementById( "version2" ); 
        	for(var i=0;i<data[0].length;i++){
        	   var option  =   new  Option(data[0][i],i);   
        	   s2.options[i] = option;  	   	 	
			} 
			
			var s3  =  document.getElementById( "count1" ); 
        	for(var i=0;i<data[1].length;i++){
        	   var option  =   new  Option(data[1][i],i);   
        	   s3.options[i] = option;  	   	 	
			} 
			var chart1 = document.getElementById("chart1");
			chart(chart1,data[2],data[3]);
        }
     }
}

function chart(divname,data,data2){
	var xx=[];xx.push(0);
	for(var i=1;i<data.length;i++){
		if(data[i]!=data[i-1]){
			xx.push(i)
		}
	}
	var index=[];
	for(var i=0;i<xx.length;i++){
		if(i==0){
//			index.push(Math.ceil(xx[i]/2));
		}else{
			index.push( Math.floor((xx[i]-xx[i-1])/2 + xx[i-1]));
		}
	}
	index.push(Math.ceil((data.length-1-xx[xx.length-1])/2 + xx[xx.length-1]))
	var chart = Highcharts.chart(divname, {
		chart: {
				zoomType: 'xy',
		},
		title: {
				text: '',
		},

	    xAxis: [
		
		{
			tickWidth:1,
			
			categories:data, 
			tickPositions:index,
			labels: {
				rotation:-30,
					style: {
						
					}
			}
		}],

		yAxis: [{ // Primary yAxis
				labels: {
						format: '{value}',
						style: {
								color: Highcharts.getOptions().colors[0]
						}
				},
				title: {
						text: '数值',
						style: {
								color: Highcharts.getOptions().colors[0]
						}
				}
				
		}],
		 tooltip: {
				pointFormat:'{point.name}  <b>{point.y}</b> '
		},
		plotOptions: {
        	spline: {
            dataLabels: {
                // 开启数据标签
                enabled: true  ,
//                formatter:function(){
//	return this.point.name+Highcharts.numberFormat(this.percentage,2)+'%';
//				},
				format:"{point.y:.4f}"     
            }
            // 关闭鼠标跟踪，对应的提示框、点击事件会失效
//            enableMouseTracking: false
        }
    }
    ,
    
		series: 

		[ {
				name: '数值',
				type: 'spline',
				data: data2,
				tooltip: {
						valueSuffix: ' '
				},findNearestPointBy:'xy'
		}]
});
	
}
function table1(data){
	var tbody1  = document.getElementById("tbody1") ; 
	s="";
	for(var i=0;i<data.length;i++){
		//var a = "\"choose("+data[i].devName+")\"";
		if(data[i].ifpass=="不通过"){
			s+="<tr bgcolor='#ef7a82'  onclick='choose()'>";
		}
		else{
			s+="<tr ondblclick='choose()'>";
		}
		s+="<td style='text-align:center'>"+data[i].TestVersion+"</td>";
		s+="<td style='text-align:center'>"+data[i].contrastTestVersion+"</td>";
		s+="<td style='text-align:center'>"+data[i].team+"</td>";
		s+="<td style='text-align:center'>"+data[i].aautype+"</td>";
		s+="<td style='text-align:center'>"+data[i].devName+"</td>";
		s+="<td style='text-align:center'>"+data[i].score+"</td>";
		s+="<td style='text-align:center'>"+data[i].improve+"</td>";
		s+="<td style='text-align:center'>"+data[i].normal+"</td>";
		s+="<td style='text-align:center'>"+data[i].bad+"</td>";
		s+="<td style='text-align:center'>"+data[i].ifpass+"</td>";
		s+="<td style='text-align:center'>"+""+"</td>";
		s+="</tr>"
	}
	tbody1.innerHTML=s;
	
}

function  chooseversion1(){
	var version=null; var s  =  document.getElementById( "version1" ); 
	version=s.options[s.selectedIndex].text;
	document.getElementById("version11").innerText=version;
}

function  chooseversion2(){
	var version=null; var s  =  document.getElementById( "version2" ); 
	version=s.options[s.selectedIndex].text;
	//window.open("1CIversion.jsp?version="+version);
	document.getElementById("version22").innerText=version;
}
function choose(){
	var td = event.srcElement;
	tr =td.parentElement; 
	var dev=tr.cells[4].innerHTML;
	var contrastTestVersion = tr.cells[1].innerHTML;
	var TestVersion = tr.cells[0].innerHTML;
	var team=tr.cells[2].innerHTML;
	team=encodeURIComponent(team);
	team=encodeURIComponent(team);
	window.open("kpi.jsp?version="+TestVersion+"&devname="+dev+"&contrastTestVersion="+contrastTestVersion+"&team="+team+"&index="+1);
}

function query(dev) 
{	
	var version = document.getElementById("version11").innerText;
	var contrastTestVersion = document.getElementById("version22").innerText;
//	var team=""
	if(version==""||contrastTestVersion==""){
		alert("您选择的版本为空");
	}else{
		if(data[0][0]==null || data[0][0]==undefined){
			t="";
		}else{
			var t = data[0][0].team;t=encodeURIComponent(t);t=encodeURIComponent(t);
		}
			window.open("kpi.jsp?version="+version+"&devname="+v+"&contrastTestVersion="+contrastTestVersion+"&team="+t+"&index="+1);
	}

}

function refresh()
{
	var count=null; var s  =  document.getElementById( "count1" ); 
	count=s.options[s.selectedIndex].text;
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = refreshcallBack;
    var url="Query1CIdevkpiInfoServlet?index="+s.selectedIndex+"&dev="+v;  
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);
}

function refreshcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
			var char = document.getElementById("chart1");
			chart(char,data[0],data[1]);
        }
     }
}

function re(){
	window.open("datapanel.jsp","_self");
}
