package servlet;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Random;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.http.client.HttpClient;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

/**
 * Servlet implementation class Mp3UploadServlet
 */
@WebServlet("/Mp3UploadServlet")
public class Mp3UploadServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private HttpClient customHttpClient = null;

    // 涓婁紶鏂囦欢淇濆瓨鐩綍
    private static final String UPLOAD_DIRECTORY = "uploads";
    // 涓婁紶閰嶇疆
    private static final int MEMORY_THRESHOLD = 1024 * 1024 * 3;  // 3MB
    private static final int MAX_FILE_SIZE = 1024 * 1024 * 50; // 50MB
    private static final int MAX_REQUEST_SIZE = 1024 * 1024 * 60; // 60MB

    /**
     * @see HttpServlet#HttpServlet()
     */
    public Mp3UploadServlet() {
        super();
    }

    /**
     * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        doPost(request, response);
    }

    /**
     * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        request.setCharacterEncoding("UTF-8");

        JSONObject result = new JSONObject();
        // 妫�鏌ヨ姹傛槸鍚︿负multipart/form-data
        if (!ServletFileUpload.isMultipartContent(request)) {
        	result.put("code", 1);
            result.put("msg", "上传失败");
            writeResponse(response, result);
            return;
        }

        try {
            // 閰嶇疆涓婁紶鍙傛暟
            DiskFileItemFactory factory = new DiskFileItemFactory();
            factory.setSizeThreshold(MEMORY_THRESHOLD);
            factory.setRepository(new File(System.getProperty("java.io.tmpdir")));

            ServletFileUpload upload = new ServletFileUpload(factory);
            upload.setFileSizeMax(MAX_FILE_SIZE);
            upload.setSizeMax(MAX_REQUEST_SIZE);
            upload.setHeaderEncoding("UTF-8");

            // 瑙ｆ瀽璇锋眰鐨勫唴瀹规彁鍙栨枃浠舵暟鎹�
            List<FileItem> formItems = upload.parseRequest(request);

            if (formItems != null && formItems.size() > 0) {
                // 鑾峰彇涓婁紶鐩綍鐨勭粷瀵硅矾寰�
                String uploadPath = getServletContext().getRealPath("") + File.separator + UPLOAD_DIRECTORY;
                File uploadDir = new File(uploadPath);
                if (!uploadDir.exists()) {
                    uploadDir.mkdir();
                }

                for (FileItem item : formItems) {
                    // 澶勭悊涓嶅湪琛ㄥ崟涓殑瀛楁
                    if (!item.isFormField()) {
                        String fileName = item.getName();
                        if (fileName != null && !fileName.isEmpty()) {
                            // 楠岃瘉鏂囦欢绫诲瀷
                            if (!isValidMp3File(fileName, item.getContentType())) {
                            	result.put("code", 1);
                                result.put("msg", "上传失败");
                                writeResponse(response, result);
                                return;
                            }

                            // 鐢熸垚鏂扮殑鏂囦欢鍚�
                            String newFileName = generateFileName() + "_" + fileName;
                            String filePath = uploadPath + File.separator + newFileName;
                            File storeFile = new File(filePath);

                            // 淇濆瓨鏂囦欢鍒扮‖鐩�
                            item.write(storeFile);

                            // 璋冪敤璇煶鍒嗘瀽鎺ュ彛
                            String apiResult = callVoiceAnalysisApi(filePath, newFileName);
                            JSONObject tempObject = new JSONObject(apiResult);
                            if(!tempObject.isNull("status")) {
                            	String tempstatus = tempObject.getString("status");
                            	if(tempstatus.equals("success")) {
                            		result.put("code", 0);
                                    result.put("msg", "上传成功,正在智能分析......\r\n"+apiResult);
//                                    result.put("info", apiResult);
                            	}else {
                            		result.put("code", 1);
                                    result.put("msg", "上传失败");
                            	}
                     
                            }else {
                                result.put("code", 1);
                                result.put("msg", "上传失败");
                            }

                            
                        }
                    }
                }
            } else {
            	result.put("code", 1);
                result.put("msg", "上传失败");
            }
        } catch (Exception ex) {
        	result.put("code", 1);
            result.put("msg", "上传失败");
            ex.printStackTrace();
        }

        writeResponse(response, result);
    }

    /**
     * 楠岃瘉鏄惁涓烘湁鏁堢殑MP3鏂囦欢
     */
    private boolean isValidMp3File(String fileName, String contentType) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }

        // 妫�鏌ユ枃浠舵墿灞曞悕
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        if (!"mp3".equals(extension)) {
            return false;
        }

        // 妫�鏌IME绫诲瀷
        if (contentType != null) {
            return contentType.equals("audio/mp3") || contentType.equals("audio/mpeg");
        }

        return true;
    }

    /**
     * 鐢熸垚闅忔満鏂囦欢鍚�
     */
    private String generateFileName() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        String str = simpleDateFormat.format(date);

        Random random = new Random();
        int rannum = (int)(random.nextDouble() * (99999999 - 10000000 + 1)) + 10000000;

        return rannum + str;
    }

    /**
     * 璋冪敤璇煶鍒嗘瀽鎺ュ彛
     */
    private String callVoiceAnalysisApi(String filePath, String fileName) {
        try {
            String url = "http://10.231.145.183:18209/api/whisper/upload";

            HttpResponse<JsonNode> httpResponse = Unirest.post(url)
                .header("accept", "application/json")
                .field("audio", new File(filePath))
                .field("sampleid", 1)
                .asJson();
            if (httpResponse.getStatus() == 200) {
                JSONObject responseObject = httpResponse.getBody().getObject();
                System.out.println("Upload API Response: " + responseObject.toString());
                return responseObject.toString();
            }

            return "{'status':'fail'}";
        } catch (Exception e) {
            e.printStackTrace();
            return "{'status':'fail'}";
        }
    }

    /**
     * 鍐欏叆鍝嶅簲
     */
    private void writeResponse(HttpServletResponse response, JSONObject result) throws IOException {
        PrintWriter out = response.getWriter();
        out.print(result.toString());
        out.flush();
        out.close();
    }
}
