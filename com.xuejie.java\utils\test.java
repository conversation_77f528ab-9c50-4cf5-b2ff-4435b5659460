package utils;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.json.JSONArray;
import org.json.JSONException;
import org.openxmlformats.schemas.drawingml.x2006.main.CTOuterShadowEffect;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;

public class test {
	public static void main(String[] args) {
		DaoCMCC dao = new DaoCMCC();
		
		String sql ="SELECT * FROM iwork2.aivonrrecord2 limit 0,100;";
		ResultSet rs0 = dao.executeQuery(sql);
		JSONArray jsonArray0 = new JSONArray();
		try {
			jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		System.out.println(jsonArray0);
//		try {
//			while(rs.next()) {
//				String dev = "";
//				if(rs.getString(5)!=null) dev = rs.getString(5);
//				String tag1 = "";
//				if(rs.getString(6)!=null) tag1 = rs.getString(6);
//				String tag2 = "";
//				if(rs.getString(7)!=null) tag2 = rs.getString(7);
//				String tag3 = "";
//				if(rs.getString(8)!=null) tag3 = rs.getString(8);
//
//				String gnbid = rs.getString(1);
//				String pci = rs.getString(2);
//				String celllocalid = rs.getString(3);
//
//				String sql2 = "update necellinfo set devname='"+dev+"',tag1='"+tag1+"',tag2='"+tag2+"',tag3='"+tag3+"' where gnbid ='"+gnbid+"' and pci = '"+pci+"' and celllocalid='"+celllocalid+"' and type=0";
//				System.out.println(sql2);
//				dao.execute(sql2);
//			}
//;		} catch (SQLException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
		 dao.close();
	}
}
