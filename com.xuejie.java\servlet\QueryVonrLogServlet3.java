package servlet;

import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Timer;
import java.util.TimerTask;


import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;



/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryVonrLogServlet3")
public class QueryVonrLogServlet3 extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;   
	 private MyThread2 myThread; 
	 private HashMap<String,String> map =  new HashMap<>();
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryVonrLogServlet3() {
        super();
        // TODO Auto-generated constructor stub
    }
    
    
	@Override
	public void init() throws ServletException {
		// TODO Auto-generated method stub
		//super.init();
//		 System.out.println("开始");
//		 this.timerTask();
	}


	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
    	Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	    String updatetime=sbf.format(d);
	    
	    LocalDateTime localTime = LocalDateTime.parse(updatetime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
		LocalDateTime newTime = localTime.plus(-72, ChronoUnit.HOURS);
		String starttime = newTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
		QueryVonrInfo(starttime,updatetime);
		
	}
	
	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}

	

	
	//GET锟斤拷锟斤拷, 锟斤拷询锟斤拷元锟斤拷锟斤拷状态
	protected void QueryVonrInfo(String starttime, String updatetime2) {		
			try {
				
				starttime=URLEncoder.encode(starttime,"utf-8"); 
				updatetime2=URLEncoder.encode(updatetime2,"utf-8"); 

				
				String url = "https://iask.zte.com.cn/api/whisper/file/query?start_time="+starttime+"&end_time="+updatetime2+"";
//				String url = "http://10.231.145.183:18209/api/whisper/inquire/time?start_time="+starttime+"&end_time="+updatetime2+"";
//				String url = "https://iask.zte.com.cn/api/whisper/inquire/time?start_time="+starttime+"&end_time="+updatetime2+"";
				System.out.println(url);
				//url=URLEncoder.encode(url,"utf-8"); 
//		        Unirest.setHttpClient(customHttpClient);
		        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
		        		.header("content-type", "application/json")
		        	    .header("accept", "application/json")
		                .asJson(); 
		        System.out.println(httpresponse.getBody().toString());
			     JSONArray array=httpresponse.getBody().getArray();
//			     System.out.println(array);
			     if(array.length()>0) {

				      Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					    String updatetime=sbf.format(d);
					      String updateSql="insert into aivonrlog(dataid,deleted,userid,uploadtime,filename,path) values";

					    for(int i=0;i<array.length();i++) {
						     JSONObject resultObject=array.getJSONObject(i);
				    		 String upload_time = "";
				    		 String path="";
				    		 String filename = "";
				    		 String employee_id = "";
				    		 int id = -1;
				    		 if(!resultObject.isNull("upload_time")) {
				    			 upload_time = resultObject.getString("upload_time") ;
								}
				    		 if(!resultObject.isNull("path")) {
				    			 path =resultObject.getString("path")  ;
								}
						     if(!resultObject.isNull("filename")) {
									filename = resultObject.getString("filename") ;
								}
						     if(!resultObject.isNull("employee_id")) {
						    	 employee_id = resultObject.getString("employee_id") ;
								}
						     if(!resultObject.isNull("id")) {
						    	 id = resultObject.getInt("id") ;
								}
						     updateSql+= "('"+String.valueOf(id)+"','"+resultObject.getBoolean("deleted")+"','"+employee_id+"','"+upload_time+"','"+filename+"','"+path+"'),";
						    	 
					    }
					    updateSql = updateSql.substring(0,updateSql.length()-1);
					     updateSql+=" on duplicate key update dataid=values(dataid),deleted=values(deleted),userid=values(userid),uploadtime=values(uploadtime),filename=values(filename)"
			     		+ ",path=values(path);";
					     DaoCMCC dao=new DaoCMCC();
//					     System.out.println(updateSql);
					     dao.execute(updateSql);
						 dao.close();
					    
			     }

		    } catch (Exception e) {
		    	e.printStackTrace();
		    }
		}

	
	public  void timerTask() {  
        new Timer().schedule(new TimerTask(){  
            @Override  
            public void run() {  
            	myThread = new MyThread2();  
	            myThread.start();	
            }},new Date(),1000*30);  
  } 
	
	  class MyThread2 extends Thread { 
		    public void run() {
		    	Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			    String updatetime=sbf.format(d);
			    
			    LocalDateTime localTime = LocalDateTime.parse(updatetime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
				LocalDateTime newTime = localTime.plus(-700, ChronoUnit.HOURS);
				String starttime = newTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//				System.out.println(starttime);
//				System.out.println(updatetime);
				
				QueryVonrInfo(starttime,updatetime);
		    	
		    }	      
		}
}




