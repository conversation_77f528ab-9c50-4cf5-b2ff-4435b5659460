var xmlHttp=false;  
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}
function xjinit(){
	
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = xjinitcallBack;
    	var url="QueryFeatureinfoServlet";  
    	xmlHttp.open("GET", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);	
}
function xjinitcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var xjdata=xmlHttp.responseText;
			xjdata=JSON.parse(xjdata); 
           	layui.use(['tree', 'util'], function(data){
    		var tree = layui.tree
    				,layer = layui.layer
    				,util = layui.util  
    				,data = xjdata[0];
    	     featuredoc(xjdata[1],"test2");
    	  //基本演示
    	  tree.render({
    	    elem: '#test1'
    	    ,data: data
    	    ,showCheckbox: false  //是否显示复选框
    	    ,id: 'demoId1'
    	    ,isJump: true //是否允许点击节点时弹出新窗口跳转
    	    ,click: function(obj){
    		  	if(obj.data.level==4)
    		  	{  
      				getfeatureParams(obj.data.parenttitle.toString(),obj.data.title.toString());
    		  	}
    		  	if(obj.data.level==1)
    		  	{  
      				xjinit();
    		  	}
    	    }
    	  });    	   
    	  //按钮事件
    	  util.event('lay-demo', {
    	    getChecked: function(othis){
    	      var checkedData = tree.getChecked('demoId1'); //获取选中节点的数据
    	      layer.alert(JSON.stringify(checkedData), {shade:0});
    	      console.log(checkedData);
    	    }
    	    ,setChecked: function(){
    	      tree.setChecked('demoId1', [12, 16]); //勾选指定节点
    	    }
    	    ,reload: function(){
    	      //重载实例
    	      tree.reload('demoId1', {  
    	      }); 
    	    }
    	  });
    	 
    	});
    	layui.use(['element', 'layer'], function(){
    			  var element = layui.element;
    			  var layer = layui.layer;
    			  //监听折叠
    			  element.on('collapse(test)', function(data){
    			    layer.msg('展开状态：'+ data.show);
    			  });
    	});    	 	
		}
	}
		
}
function featuredoc(data,divid)
{
	var s="<table class='layui-table' style='font-size:15px'>"+
					"<thead>"+
					"<tr>"+
					"<th style='text-align:center;color:white;font-size:15px;' bgcolor='#00BFFF'>版本</th>"+
										"<th style='text-align:center;color:white;font-size:15px;' bgcolor='#00BFFF'>作者</th>"+
										"<th style='text-align:center;color:white;font-size:15px;' bgcolor='#00BFFF'>更新日期</th>"+
									    //"<th style='text-align:center;font-size:15px;' bgcolor='#C1FFC1'>更新内容</th>"+
									    "</tr>"+
								        "</thead><tbody>";
	for(var i=0;i<data.length;i++)
	{
				s+="<tr style='text-align:left;'>";
				s+="<td style='font-size:15px;'>"+data[i].version+"</td>";
				s+="<td style='font-size:15px;'>"+data[i].author+"</td>";
				s+="<td style='font-size:15px;'>"+data[i].updatetime+"</td>";	
				//s+="<td style='font-size:15px;'>"+data[i].updatecontext+"</td>";	
				s+="</tr>";
	}						
	s+="</tbody></table>"   	  
    document.getElementById(divid).innerHTML=s;
    document.getElementById("title").innerHTML="版本发布信息";
}
function getfeatureParams(name1,name2)
{
		var data = {
		    name1: name1,
			name2: name2
		};
//		alert(name1);
//		alert(name2);
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = getfeatureParamscallBack;
    	var url="QueryFeatureinfoServlet";  
    	xmlHttp.open("POST", url, true);
    	xmlHttp.setRequestHeader("Content-Type","application/json"); 
    	xmlHttp.send(JSON.stringify(data));
}
function getfeatureParamscallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var xjdata=xmlHttp.responseText;
			xjdata=JSON.parse(xjdata);  
           	document.getElementById("title").innerHTML="特性Feature信息";   	
    	 	printfeatureinfo(xjdata);
		}
	}
}
function printfeatureinfo(data)
{
	var s="<hr><span class='layui-badge layui-bg-blue' style='font-size: 16px;font-weight: 400' >基本信息</span>";
	s+="<table class='layui-table'  style=font-size:15px;WORD-BREAK: break-all; WORD-WRAP: break-word'><thead><tr>"+
		   "<th style='text-align:left;' bgcolor='#D3D3D3'>参数名</th>"+
		   "<th style='text-align:left;' bgcolor='#D3D3D3'>参数表</th>"+
		   "<th style='text-align:left;' bgcolor='#D3D3D3'>参数字段</th>"+	
		   "<th style='text-align:left;' bgcolor='#D3D3D3'>参数类型</th>"+							   
		   "</tr></thead><tbody>";
	for(var i=0;i<data.length;i++)
	{
				s+="<tr style='text-align:left;'>";
				s+="<td style='font-size:15px;'>"+data[i].paramname+"</td>";
				s+="<td style='font-size:15px;'>"+data[i].moname+"</td>";
				s+="<td style='font-size:15px;'>"+data[i].paramfield+"</td>";	
				s+="<td style='font-size:15px;'>"+data[i].sort+"</td>";
				s+="</tr>";	
	}						
	s+="</tbody></table><hr>"   
	
	s+="<span class='layui-badge layui-bg-blue' style='font-size: 16px;font-weight: 400' >参数描述</span>";
	s+="<table class='layui-table' style='font-size:15px;WORD-BREAK: break-all; WORD-WRAP: break-word'><tbody>";
	for(var i=0;i<data.length;i++)
	{
				s+="<tr style='text-align:left;'>";
				s+="<td style='font-size:15px;'>"+data[i].paramfield+"</td>";
				s+="<td style='font-size:15px;'>"+data[i].paramdescription+"</td>";	
				//s+="<td style='font-size:15px;'>"+data[i].updatecontext+"</td>";
				s+="</tr>";	
	}		
	s+="</tbody></table><hr><hr>"  
	
	s+="<span class='layui-badge layui-bg-blue' style='font-size: 16px;font-weight: 400' >参数取值</span>";
	s+="<table class='layui-table' style='font-size:15px;WORD-BREAK: break-all; WORD-WRAP: break-word'><thead><tr>"+
		   "<th style='text-align:left;' bgcolor='#D3D3D3'>参数</th>"+
		   "<th style='text-align:left;' bgcolor='#D3D3D3'>取值范围</th>"+
		   "<th style='text-align:left;' bgcolor='#D3D3D3'>默认值</th>"+	
		   "<th style='text-align:left;' bgcolor='#D3D3D3'>推荐值</th>"+							   
		   "</tr></thead><tbody>";
	for(var i=0;i<data.length;i++)
	{
				s+="<tr style='text-align:left;'>";
				s+="<td style='font-size:15px;'>"+data[i].paramfield+"</td>";
				s+="<td style='font-size:15px;'>"+data[i].valuerange+"</td>";
				s+="<td style='font-size:15px;'>"+data[i].defaultvalue+"</td>";	
				s+="<td style='font-size:15px;'>"+data[i].recommendvalue+"</td>";
				s+="</tr>";	
	}						
	s+="</tbody></table><hr>" 
	
	s+="<span class='layui-badge layui-bg-blue' style='font-size: 16px;font-weight: 400' >调整原则</span>";
	s+="<table class='layui-table' style='font-size:15px;WORD-BREAK: break-all; WORD-WRAP: break-word;'><tbody>";
	for(var i=0;i<data.length;i++)
	{
				s+="<tr style='text-align:left;'>";
				s+="<td style='font-size:15px;'>"+data[i].paramfield+"</td>";
				s+="<td style='font-size:15px;'>"+data[i].adjustp+"</td>";	
				s+="<td style='font-size:15px;'>"+data[i].remarks+"</td>";	
				s+="</tr>";	
	}		
	s+="</tbody></table><hr><hr>" 
	
	document.getElementById("test2").innerHTML=s;	
}