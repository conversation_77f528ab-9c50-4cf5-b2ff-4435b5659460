<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layui Table with Nested Table in Collapsible Content</title>
    <!-- 引入 Layui 的 CSS 文件 -->
<link href="addtask/layui-v2.8.3/layui/css/layui.css" type="text/css" rel="stylesheet">
    <style>
        /* 折叠内容样式 */
      .collapsible-content td {
            background-color: #f9f9f9;
            padding: 10px;
        }
    </style>
</head>

<body>
    <!-- 主表格容器 -->
    <table id="mainTable" lay-filter="mainTableFilter"></table>

    <!-- 引入 Layui 的 JavaScript 文件 -->
    <script src="layui/layui.min.js"></script>
    <script type="text/javascript" src="addtask/layui-v2.8.3/layui/layui.js"></script>
    
    <script>
        layui.use(['table'], function () {
            var table = layui.table;

            // 渲染主表格
            table.render({
                elem: '#mainTable',
                cols: [[
                    { field: 'id', title: 'ID' },
                    { field: 'name', title: '名称' },
                    { title: '操作', toolbar: '#barDemo' }
                ]],
                data: [
                    {
                        id: 1,
                        name: '项目 1',
                        subData: [
                            { subId: 11, subName: '子项 1-1' },
                            { subId: 12, subName: '子项 1-2' }
                        ]
                    },
                    {
                        id: 2,
                        name: '项目 2',
                        subData: [
                            { subId: 21, subName: '子项 2-1' },
                            { subId: 22, subName: '子项 2-2' }
                        ]
                    }
                ]
            });

            // 监听主表格工具条
            table.on('tool(mainTableFilter)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    var tr = obj.tr;
                    var nextTr = tr.next();
                    if (nextTr.hasClass('collapsible-content')) {
                        // 如果已经展开，就折叠
                        nextTr.remove();
                    } else {
                        // 如果未展开，就展开
                        var subTableId = `subTable-${data.id}`;
                        var content = `<tr class="collapsible-content"><td colspan="3"><table id="${subTableId}" lay-filter="${subTableId}-filter"></table></td></tr>`;
                        tr.after(content);

                        // 渲染子表格
                        table.render({
                            elem: `#${subTableId}`,
                            cols: [[
                                { field: 'subId', title: '子 ID' },
                                { field: 'subName', title: '子名称' }
                            ]],
                            data: data.subData
                        });
                    }
                }
            });
        });
    </script>
    <!-- 工具条模板 -->
    <script type="text/html" id="barDemo">
        <a class="layui-btn layui-btn-xs" lay-event="detail">查看详情</a>
    </script>
</body>

</html>