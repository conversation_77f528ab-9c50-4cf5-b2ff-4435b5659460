$(function () {
    $('input').bind('input propertychange', function () {
        $('.commonTable tbody tr').hide()
            .filter(":contains('" + ($(this).val()) + "')").show();
    });

    $("#refreshBtn").click(function () {
        init();
    });
    setInterval(tick, 1000);
});
function tick(){
    var today = new Date();
    document.getElementById("localtime").innerHTML = showLocale(today)+"<font color=\"#ffffff\">  (后台刷新周期:10分钟)</font>";
}
function showLocale(objD){
    var str,colorhead,colorfoot;
    var yy = objD.getYear();
    if(yy<1900) yy = yy+1900;
    var MM = objD.getMonth()+1;
    if(MM<10) MM = '0' + MM;
    var dd = objD.getDate();
    if(dd<10) dd = '0' + dd;
    var hh = objD.getHours();
    if(hh<10) hh = '0' + hh;
    var mm = objD.getMinutes();
    if(mm<10) mm = '0' + mm;
    var ss = objD.getSeconds();
    if(ss<10) ss = '0' + ss;
    var ww = objD.getDay();
    if  ( ww==0 )  colorhead="<font color=\"#ffffff\">";
    if  ( ww > 0 && ww < 6 )  colorhead="<font color=\"#ffffff\">";
    if  ( ww==6 )  colorhead="<font color=\"#ffffff\">";
    if  (ww==0)  ww="星期日";
    if  (ww==1)  ww="星期一";
    if  (ww==2)  ww="星期二";
    if  (ww==3)  ww="星期三";
    if  (ww==4)  ww="星期四";
    if  (ww==5)  ww="星期五";
    if  (ww==6)  ww="星期六";
    colorfoot="</font>"
    str = colorhead + yy + "-" + MM + "-" + dd + " " + hh + ":" + mm + ":" + ss + "  " + ww + colorfoot;
    return(str);
}
var xmlHttp = false;
function createXMLHttpRequest() {
	if (window.ActiveXObject) {
		xmlHttp = new ActiveXObject("Microsoft.XMLHTTP");
	}
	else if (window.XMLHttpRequest) {
		xmlHttp = new XMLHttpRequest();
	}
}
function init() {
	
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = initcallBack;
    var url="QueryENVInfoServlet";  
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);
}
function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data=JSON.parse(data);
			outdevinfo(data[0]);
			
			devchart("vswdiv",data[1],"主控板类型",data[0][0].vswtypenum,data[0][0].vswnum);
			devchart("vswdiv2",data[1],"主控板类型",data[0][0].vswtypenum,data[0][0].vswnum);
			devchart("vbpdiv",data[2],"基带板类型",data[0][0].vbptypenum,data[0][0].vbpnum);
			devchart("aaudiv",data[3],"AAU/RRU类型",data[0][0].aautypenum,data[0][0].aaunum);
			//initTable(data[4]);	
			table1(data[4]);
//			devcolumnchart(data[5],data[6],"gnbautonum","各团队站点分布");
		}
	}
}
function outdevinfo(myData)
{
	document.getElementById("devnum").innerHTML = myData[0].gnbnum;
	document.getElementById("gnbofnormal").innerHTML = myData[0].gnbofnormal;
	document.getElementById("gnbofabnormal").innerHTML = myData[0].gnbofabnormal;
	document.getElementById("updatetime").innerHTML = myData[0].updatetime;
	$("#devnum ").numberRockInt({
        initNumber: 0,
        lastNumber: myData[0].gnbnum,
        duration: 3000,
        step: 5
    });
}
function table1(data){
	var tbody1  = document.getElementById("envinfo") ; 
	s="";
	
	for(var i=0;i<data.length;i++){
		s+="<tr>"
		s+="<td  ondblclick='a()' style='text-align:center;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>"+(i+1)+"</td>";
		s+="<td ondblclick='a()' style='text-align:center;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>"+data[i].ume+"</td>";
		s+="<td ondblclick='a()' style='text-align:center;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>"+data[i].gnbid+"</td>";
		if(data[i].constatus=="断链"){
			s+="<td ondblclick='a()' style='text-align:center;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'><span style='color: #FF0000;'>" +data[i].constatus + "</span></td>";
		}else if(data[i].constatus=="正常"){
			s+="<td ondblclick='a()' style='text-align:center;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>"+data[i].constatus+"</td>";
		}
		s+="<td ondblclick='a()' style='text-align:center;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>"+data[i].version+"</td>";
		s+="<td ondblclick='a()' style='text-align:center;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>"+data[i].vsw+"</td>";
		s+="<td ondblclick='a()' style='text-align:center;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>"+data[i].vbp+"</td>";
		s+="<td ondblclick='a()' style='text-align:center;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>"+data[i].aau+"</td>";
		s+="<td ondblclick='a()' style='text-align:center;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>"+data[i].pci+"</td>";
		s+="<td ondblclick='a()' style='text-align:center;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>"+data[i].team+"</td>";
		s+="<td ondblclick='a()' style='text-align:center;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>"+data[i].isauto+"</td>";
		s+="</tr>"
	}
	
	tbody1.innerHTML=s;
}

function initTable(myData) {
    
    var HTML = "<thead>\n" +
        "        <td style='width:5%;' title=\"序号\">序号</td>\n" +
        "        <td style='width:5%;' title=\"基站ID\">基站ID</td>\n" +
        "        <td style='width:5%;' title=\"状态\">状态</td>\n" +
        "        <td style='width:12%;' title=\"版本\">版本</td>\n" +
        "        <td style='width:5%;' title=\"主控板\">主控板</td>\n" +
        "        <td title=\"基带板\">基带板</td>\n" +
        "        <td title=\"AAU*RRU\">AAU*RRU</td>\n" +
        "        <td title=\"小区\">小区</td>\n" +
        //"        <td title=\"环境位置\">环境位置</td>\n" +
        "		 <td style='width:5%;' title=\"团队\">团队</td>\n" +
        "		 <td style='width:5%;' title=\"备注\">备注</td>\n" +
        //"		 <td title=\"更新时间\">更新时间</td>\n" +
        "        <td style='width:5%;' title=\"网管\">网管</td>\n" +
        "        </thead>\n" +
        "        <tbody>\n";
        
    $(myData).each(function (index, ele) {
        	HTML += "<tr>\n" +
            "            <td title='"+(index + 1)+"' style='width:5%;white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>" + (index + 1) + "</td>\n" +
            "            <td title='"+ele['gnbid']+"' style='white-space:nowrap;overflow:hidden;text-overflow: ellipsis' onclick='a()'>" + ele['gnbid'] + "</td>\n" ;

            if(ele['constatus']=="正常")
            {	HTML+="  <td title='"+ele['constatus']+"' style='white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>" + ele['constatus'] + "</td>\n";}
            else
            { 	HTML+=  "<td title='"+ele['constatus']+"' style='white-space:nowrap;overflow:hidden;text-overflow: ellipsis'><span style='color: #FF0000;'>" + ele['constatus'] + "</span></td>\n";}
            HTML+=
            "            <td title='"+ele['version']+"' style='white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>" + ele['version'] + "</td>\n" +
            "            <td title='"+ele['vsw']+"' style='white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>" + ele['vsw'] + "</td>\n" +
            "            <td title='"+ele['vbp']+"' style='white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>" + ele['vbp'] + "</td>\n" +
            "            <td title='"+ele['aau']+"' style='white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>" + ele['aau'] + "</td>\n" +
          "            <td title='"+ele['pci']+"' style='white-space:nowrap;overflow:hidden;text-overflow: ellipsis'>" + ele['pci'] + "</td>\n" +
            "            <td title='"+ele['team']+"' style='white-space:nowrap;overflow:hidden;text-overflow: ellipsis'><span style='color: #00cc00;'>" + ele['team']+ "</span></td>\n"+
            "            <td title='"+ele['isauto']+"' style='white-space:nowrap;overflow:hidden;text-overflow: ellipsis'><span style='color: #00cc00;'>" + ele['isauto']+ "</span></td>\n";
                        "            <td title='"+ele['ume']+"' style='white-space:nowrap;overflow:hidden;text-overflow: ellipsis'><span style='color: #00cc00;'>" + ele['ume']+ "</span></td>\n";

            //"            <td><span style='color: #00cc00;'>" + ele['updatetime']+ "</span></td>\n";
    });
    HTML += "</tbody>";
    $('.commonTable').html(HTML);

    $('.commonTable tbody tr').hide()
        .filter(":contains('" + ($("#searchText").val()) + "')").show();

}

function devchart(divname,data,titlename,typenum,num)
{

//Highcharts.chart(divname, {
//		chart: {
//				plotBackgroundColor: '#06182d',
//				plotBorderWidth: null,
//				plotShadow: false,
//				backgroundColor: '#06182d',
//				type: 'pie'
//		},
//		title: {
//				text: titlename,
//				style:{color:'#0e94ea'}
//		},
//		subtitle: {
//				text: titlename+typenum+'类,设备总数:'+num,
//				style:{color:'#0e94ea'}
//		},
//		tooltip: {
//				pointFormat:'{point.name}  <b>{point.y}</b> '
//		},
//		plotOptions: {
//				pie: {
//						size:'90%',
//						allowPointSelect: true,
////						distance:-50,
//						cursor: 'pointer',
//						dataLabels: {
//								width:'2px',
//								enabled: false,
//								distance:-50,
////								format: '<b>{point.name}</b>: {point.percentage:.1f} %',
////								format: '<b>{point.name}</b>',
//								style: {
//										color: (Highcharts.theme && Highcharts.theme.contrastTextColor) || 'black'
//								}
//						}
//				}
//		},
//		series: [{
//				name: 'Brands',
//				colorByPoint: true,
//				data: data
//		}]
//});
var chart = Highcharts.chart(divname,{
	    chart: {
	        type: 'column',
	        backgroundColor: '#06182d'
	    },
	    title: {
	       	text: titlename,
		    style:{color:'#0e94ea'}
	    },
	    	subtitle: {
			text: titlename+typenum+'类,设备总数:'+num,
			style:{color:'#0e94ea'}
		},
	    xAxis: {
			type:'category',
	        crosshair: true,
	        labels: { style: { color: 'white' } },
	    },
	    tooltip: {
				pointFormat:'{point.name}  <b>{point.y}</b> '
		},
	    yAxis: {
	        min: 0,
	        title: {
	            text: '数量',
	            style:{color: 'white'}
	        },
	        labels: { style: { color: 'white' } },
	    },
	    	legend: 
				{
//			        align: 'bottom',
//			        x: -30,
//			        verticalAlign: 'top',
//			        y: 25,
//			        floating: true,
			        backgroundColor: (Highcharts.theme && Highcharts.theme.background2) || 'white',
//			        borderColor: '#CCC',
			        borderWidth: 1,
			        shadow: false
			    },
	    
	    series: [{
	        name: titlename,
	        data:  data,
	        dataLabels: {
	            enabled: true,
	            color: 'white',
	            align: 'center',
	        }
	    }]
	});
}


function devcolumnchart(data,data1,divname,titlename)
{
		var chart = Highcharts.chart(divname,{
	    chart: {
	        type: 'column',
	        backgroundColor: '#06182d'
	    },
	    title: {
	        text: titlename,
	        style:{color:'#0e94ea'}
	    },
	    xAxis: {
			type:'category',
	        crosshair: true,
	       labels: { style: { color: 'white' } },
	    },
	    yAxis: {
	        min: 0,
	        labels: { style: { color: 'white' } },

	        
	        title: {
	            text: '站点数',
	            style:{color:'#0e94ea'}
	        },
			stackLabels: {  // 堆叠数据标签
				enabled: true,
				style: {
					fontWeight: 'bold',
					color: (Highcharts.theme && Highcharts.theme.textColor) || 'gray'
				}
			}
	    },

		plotOptions: {
			column: {
				stacking: 'normal',
				dataLabels: {
					enabled: true,
					color: (Highcharts.theme && Highcharts.theme.dataLabelsColor) || 'white',
					style: {
						// 如果不需要数据标签阴影，可以将 textOutline 设置为 'none'
						textOutline: '1px 1px black'
					}
				}
			}
		},
	legend: 
	{
        align: 'right',
        x: -30,
        verticalAlign: 'top',
        y: 25,
        floating: true,
        backgroundColor: (Highcharts.theme && Highcharts.theme.background2) || 'white',
        borderColor: '#CCC',
        borderWidth: 1,
        shadow: false
    },
		colors:['#00FA9A','#FF6464'],
	    series: [{
	        name: '自动化',
	        data:  data1,
	        style:{color:'#0e94ea'}
	    },{
		 name: '未自动化',
	        data:  data,
	        style:{color:'#0e94ea'}
		
	}]
	});
}



function vswdisplay(){
	var vswdiv=document.getElementById("vswdiv")
	if (vswdiv.style.display=="none"){
		vswdiv.style.display="block"
	}else
		vswdiv.style.display='none'
}

function a(){
	var td = event.srcElement;
	tr = td.parentElement;
	//var ume=tr.cells[1].innerText;
	var gnbid=tr.cells[2].innerText;
//	alert(ume);alert(gnbid);
//	alert("行号：" + (td.parentElement.rowIndex) + "，列号：" + td.cellIndex);
//	alert(td.innerHTML);
	window.open("gnb.jsp?gnbid="+gnbid);
}

