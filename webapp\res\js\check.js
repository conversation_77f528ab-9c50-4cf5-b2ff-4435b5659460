var xmlHttp = false;
function createXMLHttpRequest() {
	if (window.ActiveXObject) {
		xmlHttp = new ActiveXObject("Microsoft.XMLHTTP");
	}
	else if (window.XMLHttpRequest) {
		xmlHttp = new XMLHttpRequest();
	}
}


function queryinfo()
{
	var td = event.srcElement;
	tr =td.parentElement; 
	var gnbid=tr.cells[0].innerText;
	var pci = tr.cells[1].innerText;
	var feature = tr.cells[2].innerText;
	gnbid=encodeURIComponent(gnbid);
	pci=encodeURIComponent(pci);
	feature=encodeURIComponent(feature);

	createXMLHttpRequest();
    xmlHttp.onreadystatechange = queryinfocallBack; 
    var url=encodeURI("QueryFeatureCheck2?gnbid="+gnbid+"&feature="+feature+"&pci="+pci);
//    url=encodeURI(url); 
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}


function queryinfocallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			document.getElementById( "featureresult" ).innerHTML=""; 
			
			if(data.code==1){
				document.getElementById("paramjuti").style.display="none";
				alert(data.message);
			}else{
				document.getElementById("paramjuti").style.display="inline-block";
				queryresult(data.listresult);
			}
			
			}
	}
}

function queryresult(data)
{
	var featureresult  =  document.getElementById( "featureresult" ); 
	var s="<thead><th>mocname</th><th>ldn</th><th>paramname</th><th>paramvalue</th><th>recommendvalue</th><th>isconsistent</th></thead>";
	s+="<tbody>";
	for(var i=0;i<data.length;i++){
		var color = (data[i].isconsistent=='0')?"#FFC0CB":"#F7F7F7";
//		alert(color);
		s+="<tr bgcolor='"+color+"'>";
		s+="<td>"+data[i].mocname+"</td>";
		s+="<td>"+data[i].ldn+"</td>";
		s+="<td>"+data[i].paramname+"</td>";
		s+="<td>"+data[i].paramvalue+"</td>";
		s+="<td>"+data[i].recommendvalue+"</td>";
		s+="<td>"+data[i].isconsistent+"</td>";
		s+="</tr>"
	}
	s+="</tbody>"
	featureresult.innerHTML=s;
}

function queryallinfo(){
	var gnbid1=null; 
	var feature1 = null;
	var gnbid  =  document.getElementById( "gnbinfo" ); 
	gnbid1=gnbid.innerText;
	if(gnbid1==""){
		alert("请输入网元ID");
	}else{
		document.getElementById("loading").style.display = 'block';

		createXMLHttpRequest();
	    xmlHttp.onreadystatechange = queryallinfocallBack;   
	    gnbid1=encodeURIComponent(gnbid1);
	    var url=encodeURI("QueryAllFeatureCheck?gnbid="+gnbid1);
	//    url=encodeURI(url); 
	    xmlHttp.open("POST", url, true);
	    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
	    xmlHttp.send(null);
	}
}

function queryallinfocallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			document.getElementById("loading").style.display = 'none';

			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			document.getElementById( "featureresult" ).innerHTML=""; 
			
			queryallresult(data.listfeature);
			
		}
	}
}

function queryallresult(data)
{
	var featureresult  =  document.getElementById( "featureresult" ); 
	var s="<thead><th>场景</th><th>特性Feature</th><th>是否一致</th><th>备注</th></thead>";
	s+="<tbody>";
	for(var i=0;i<data.length;i++){
		var color = (data[i].isconsistent=='0')?"#FFC0CB":"#F7F7F7";
//		alert(color);
		s+="<tr bgcolor='"+color+"'>";
		s+="<td>"+data[i].subscenario+"</td>";
		s+="<td>"+data[i].featurename+"</td>";
		s+="<td>"+data[i].isconsistent+"</td>";
		s+="<td>"+data[i].remark+"</td>";
		s+="</tr>"
	}
	s+="</tbody>"
	featureresult.innerHTML=s;
}

function checkinfo()
{
	var gnbids  =  document.getElementById( "gnbinfo" ).innerText; 
	var feature  =  document.getElementById( "featureinfo" ).innerText; 
    if(gnbids==""||feature==""){
		alert("网元/功能/小区为空");
	}
	else{
		var devname1=document.getElementById("result")
	    if (devname1.style.display=="none"){
			devname1.style.display="inline-block"
		}
		gnbid=gnbids.split("-")[0];pci=gnbids.split("-")[1];
		add_row(gnbid,pci,feature);
	}

	
//    document.getElementById("paramjuti").style.display="none";
}

function add_row(gnbid,pci,feature){
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = configinfoquerycallBack;
    document.getElementById("loading").style.display = 'block';
    var url=encodeURI("ConfigInfoQuery?gnbid="+gnbid+"&feature="+feature +"&pci="+pci);
    url=encodeURI(url); 
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}
function clean(){
	document.getElementById("paramresult").innerHTML="";
}


function configinfoquerycallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);	
			document.getElementById("loading").style.display = 'none';
			if(data[0].length==0){
				alert("未找到适配参数套");
			}else {
				var d =$("#paramresult tr").length;
				for(var i=0;i<data[0].length;i++){
					var tr = document.createElement("tr");
					var td1 = document.createElement("td");
					td1.innerText=data[0][i].gnbid;
					var td2 = document.createElement("td");
					td2.innerText=data[0][i].pci;
					var td3 = document.createElement("td");
					td3.innerText=data[0][i].featurename;
					var td4 = document.createElement("td");
					var td4neirong = "<input id='"+(d+i+1)+"' value='"+(d+i+1)+"' checked type='checkbox'>";
					td4.innerHTML=td4neirong;
					tr.appendChild(td1);
					tr.appendChild(td2);
					tr.appendChild(td3);
					tr.appendChild(td4);
					document.getElementById("paramresult").appendChild(tr);
				}
			}
		}
	}
}

function addfeature()
{
	var gnbids  =  document.getElementById( "gnbinfo" ).innerText; 
	var feature  =  document.getElementById( "featureinfo" ).innerText; 
    if(gnbids==""||feature==""){
		alert("网元/功能/小区为空");
	}else{
		var devname1=document.getElementById("result")
	    if (devname1.style.display=="none"){
			devname1.style.display="inline-block"
		}
		gnbid=gnbids.split("-")[0];
		pci=gnbids.split("-")[1];
		var d =$("#paramresult tr").length;
		var tr = document.createElement("tr");
		var td1 = document.createElement("td");
		td1.innerText=gnbid;
		var td2 = document.createElement("td");
		td2.innerText=pci;
		var td3 = document.createElement("td");
		td3.innerText=feature;
		var td4 = document.createElement("td");
		var td4neirong = "<input id='"+(d+1)+"' value='"+(d+1)+"' checked type='checkbox'>";
		td4.innerHTML=td4neirong;
		tr.appendChild(td1);
		tr.appendChild(td2);
		tr.appendChild(td3);
		tr.appendChild(td4);
		document.getElementById("paramresult").appendChild(tr);
	}
    document.getElementById("paramjuti").style.display="none";
}


function query()
{
	var t = document.getElementById( "paramresult" );
	var gnbids="",pcis="",features="";
	var rows = t.rows;
	for(var i=0;i<rows.length;i++){
		ischeck = document.getElementById((i+1)).checked;	
		if(ischeck==false) continue;
		for(var j=0;j<rows[i].cells.length;j++){

			if(j==0){
				if(i==rows.length-1){
					gnbids = gnbids+rows[i].cells[j].innerText;
				}else{
					gnbids = gnbids+rows[i].cells[j].innerText+"#";
				}
				
			}else if(j==1){
				if(i==rows.length-1){
					pcis = pcis+rows[i].cells[j].innerText;
				}else{
					pcis = pcis+rows[i].cells[j].innerText+"#";
				}
				
			}else if(j==2){
				if(i==rows.length-1){
					features = features+rows[i].cells[j].innerText;
				}else{
					features = features+rows[i].cells[j].innerText+"#";
				}
				
			}
		}
	}
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = querycallBack; 
    gnbids=encodeURIComponent(gnbids);
    pcis=encodeURIComponent(pcis);
    features=encodeURIComponent(features);
	document.getElementById("loading").style.display = 'block';
    var url=encodeURI("QueryFeatureCheck?gnbid="+gnbids+"&feature="+features+"&pci="+pcis);
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}


function querycallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);	
			document.getElementById("loading").style.display = 'none';
			var t = document.getElementById( "paramresult" );
			var s ="";
			for(var i =0;i<data.length;i++){
				s+="<tr>"
				s+="<td title='"+data[i].gnbid+"' style='width:10%; overflow:hidden;word-break:keep-all;white-space:nowrap;text-align:center;'>"+data[i].gnbid+"</td>"
				s+="<td title='"+data[i].pci+"' style='width:10%;overflow:hidden;word-break:keep-all;white-space:nowrap;text-align:center;'>"+data[i].pci+"</td>"
				s+="<td title='"+data[i].featurename+"' style='width:55%;overflow:hidden;word-break:keep-all;white-space:nowrap;text-align:center;'>"+data[i].featurename+"</td>"
				s+="<td title='"+data[i].message+"' style='width:15%;overflow:hidden;word-break:keep-all;white-space:nowrap;text-align:center;'>"+data[i].message+"</td>"
				s+="<td title='"+data[i].flag+"' style='width:15%;overflow:hidden;word-break:keep-all;white-space:nowrap;text-align:center;'>"+data[i].flag+"</td>"
				s+="<td  style='width:10%;overflow:hidden;word-break:keep-all;white-space:nowrap;text-align:center;'  onclick='queryinfo()'>"+"查看"+"</td>"
				s+="</tr>"
			}
			t.innerHTML=s;
		}
	}
}