<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layui 折叠表格示例</title>
    <link rel="stylesheet" href="https://cdn.staticfile.org/layui/2.6.8/css/layui.min.css">
</head>

<body>
    <table id="demo" lay-filter="test"></table>

    <script src="https://cdn.staticfile.org/layui/2.6.8/layui.min.js"></script>
    <script>
        layui.use('table', function () {
            var table = layui.table;

            // 模拟数据
            var data = [
                {
                    id: 1,
                    title: '分组 1',
                    children: [
                        { id: 11, name: '子项 1-1' },
                        { id: 12, name: '子项 1-2' }
                    ]
                },
                {
                    id: 2,
                    title: '分组 2',
                    children: [
                        { id: 21, name: '子项 2-1' },
                        { id: 22, name: '子项 2-2' }
                    ]
                }
            ];

            // 渲染表格
            table.render({
                elem: '#demo',
                data: data,
                treeColIndex: 1, // 树形图标显示在第几列
                treeSpid: 'id', // 自定义每个树节点的唯一标识字段
                treeIdName: 'id', // 自定义节点 id 字段名
                treePidName: 'pid', // 自定义父节点 id 字段名
                cols: [
                    [
                        { field: 'id', title: 'ID' },
                        { field: 'title', title: '分组名称' },
                        { field: 'name', title: '子项名称' }
                    ]
                ]
            });
        });
    </script>
</body>

</html>