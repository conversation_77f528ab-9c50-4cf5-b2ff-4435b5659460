/**
 * 
 */

var myjson;
layui.use(function(){
  var laypage=layui.laypage;
  var layer = layui.layer;
  var table = layui.table;
  // 渲染
  table.render({
	type:'post',
    elem: '#table0',
    url:'QueryUsecaseInfoServlet2', // 此处为静态模拟数据，实际使用时需换成真实接口
    even: false,
    toolbar: '#page0',
    id:'testReload0',
    request: {
          pageName: 'pageIndex',  // 页码的参数名称，默认：page
          limitName: 'pageSize'   // 每页数据量的参数名，默认：limit
	
	  },
	response: {
          statusName: 'code',     // 规定数据状态的字段名称，默认：code
          statusCode: 0,          // 规定成功的状态码，默认：0
          countName: 'ptype0', // 规定数据总数的字段名称，默认：count
          msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
          dataName: 'data0' ,       // 规定数据列表的字段名称，默认：data
	  },
    cols: [[
        {field:'xuhao', title:'序号', width:'5%',type:'numbers'},
        {field:'team', title:'团队',width:'10%'},
        {field:'id', title:'用例标识',width:'10%'},
        {field:'title', title:'标题'},
        {field:'state', title:'状态',width:'5%'},
        {field:'hasautomated', title:'是否已自动化',width:'10%'},
        {field:'author', title:'创建人', width:'10%'},
        {field:'executephase', title:'规划执行阶段',width:'10%'},
//       	{field:'parambushu', title:'是否执行',width:'10%'},
	      {field:'parambushu', width:'10%', title: '是否执行', templet: function(d){
	        if(d.parambushu == 1){
	          return '<span style="color: green">执行</span>';
	        } else {
	          return '<span style="color: red">未执行</span>';
	        }
	      }},
      ]],
    
   page: {layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
	    curr: 1,      // 设置默认起始页1
	    groups: 5,   //只显示10个连续页码,就是说显示10个可见页其他的省略
	    first: false, // 不显示首页
	    last: false   // 不显示尾页
		},
   text:{
	    none:'暂无相关数据'},
	     limit: 20,
		limits: [5,10,15,20,100,1000]
  });
  //2级CItable展示
    table.render({
	type:'post',
    elem: '#table1',
    url:'QueryUsecaseInfoServlet2', // 此处为静态模拟数据，实际使用时需换成真实接口
    even: false,
    toolbar: '#page1',
    id:'testReload1',
    request: {
          pageName: 'pageIndex',  // 页码的参数名称，默认：page
          limitName: 'pageSize'   // 每页数据量的参数名，默认：limit
	
	  },
	response: {
          statusName: 'code',     // 规定数据状态的字段名称，默认：code
          statusCode: 0,          // 规定成功的状态码，默认：0
          countName: 'ptype1', // 规定数据总数的字段名称，默认：count
          msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
          dataName: 'data1' ,       // 规定数据列表的字段名称，默认：data
	  },
    cols: [[
        {field:'xuhao', title:'序号', width:'5%',type:'numbers'},
        {field:'team', title:'团队',width:'10%'},
        {field:'id', title:'用例标识',width:'10%'},
        {field:'title', title:'标题'},
        {field:'state', title:'状态',width:'5%'},
        {field:'hasautomated', title:'是否已自动化',width:'10%'},
        {field:'author', title:'创建人', width:'10%'},
        {field:'performancetopic', title:'特性组', width:'10%'},

        {field:'executephase', title:'规划执行阶段',width:'10%'},
//       	{field:'parambushu', title:'是否执行',width:'10%'},
	      {field:'parambushu', width:'10%', title: '是否执行', templet: function(d){
	        if(d.parambushu == 1){
	          return '<span style="color: green">执行</span>';
	        } else {
	          return '<span style="color: red">未执行</span>';
	        }
	      }},
      ]],
    
   page: {layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
	    curr: 1,      // 设置默认起始页1
	    groups: 5,   //只显示10个连续页码,就是说显示10个可见页其他的省略
	    first: false, // 不显示首页
	    last: false   // 不显示尾页
		},
   text:{
	    none:'暂无相关数据'},
	     limit: 20,
		limits: [5,10,15,20,100,1000]
  });
  
    //3级CItable展示
    table.render({
	type:'post',
    elem: '#table2',
    url:'QueryUsecaseInfoServlet2', // 此处为静态模拟数据，实际使用时需换成真实接口
    even: false,
    toolbar: '#page2',
    id:'testReload2',
    request: {
          pageName: 'pageIndex',  // 页码的参数名称，默认：page
          limitName: 'pageSize'   // 每页数据量的参数名，默认：limit
	
	  },
	response: {
          statusName: 'code',     // 规定数据状态的字段名称，默认：code
          statusCode: 0,          // 规定成功的状态码，默认：0
          countName: 'ptype2', // 规定数据总数的字段名称，默认：count
          msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
          dataName: 'data2' ,       // 规定数据列表的字段名称，默认：data
	  },
    cols: [[
        {field:'xuhao', title:'序号', width:'5%',type:'numbers'},
        {field:'team', title:'团队',width:'10%'},
        {field:'id', title:'用例标识',width:'10%'},
        {field:'title', title:'标题'},
        {field:'state', title:'状态',width:'5%'},
        {field:'hasautomated', title:'是否已自动化',width:'10%'},
        {field:'author', title:'创建人', width:'10%'},
        {field:'performancetopic', title:'特性组', width:'10%'},
        
        {field:'executephase', title:'规划执行阶段',width:'10%'}, 
	      {field:'parambushu', width:'10%', title: '是否执行', templet: function(d){
	        if(d.parambushu == 1){
	          return '<span style="color: green">执行</span>';
	        } else {
	          return '<span style="color: red">未执行</span>';
	        }
	      }},
      ]],
    
   page: {layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
	    curr: 1,      // 设置默认起始页1
	    groups: 5,   //只显示10个连续页码,就是说显示10个可见页其他的省略
	    first: false, // 不显示首页
	    last: false   // 不显示尾页
		},
   text:{
	    none:'暂无相关数据'},
	     limit: 20,
		limits: [5,10,15,20,100,1000]
  });
  
      //3级CItable展示
    table.render({
	type:'post',
    elem: '#table3',
    url:'QueryUsecaseInfoServlet2', // 此处为静态模拟数据，实际使用时需换成真实接口
    even: false,
    toolbar: '#page3',
    id:'testReload3',
    request: {
          pageName: 'pageIndex',  // 页码的参数名称，默认：page
          limitName: 'pageSize'   // 每页数据量的参数名，默认：limit
	
	  },
	response: {
          statusName: 'code',     // 规定数据状态的字段名称，默认：code
          statusCode: 0,          // 规定成功的状态码，默认：0
          countName: 'ptype3', // 规定数据总数的字段名称，默认：count
          msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
          dataName: 'data3' ,       // 规定数据列表的字段名称，默认：data
	  },
    cols: [[
        {field:'xuhao', title:'序号', width:'5%',type:'numbers'},
        {field:'team', title:'团队',width:'10%'},
        {field:'id', title:'用例标识',width:'10%'},
        {field:'title', title:'标题'},
        {field:'state', title:'状态',width:'5%'},
        {field:'hasautomated', title:'是否已自动化',width:'10%'},
        {field:'author', title:'创建人', width:'10%'},
        {field:'executephase', title:'规划执行阶段',width:'10%'},
//               	{field:'parambushu', title:'是否执行',width:'10%'},
	      {field:'parambushu', width:'10%', title: '是否执行', templet: function(d){
	        if(d.parambushu == 1){
	          return '<span style="color: green">执行</span>';
	        } else {
	          return '<span style="color: red">未执行</span>';
	        }
	      }},
      ]],
    
   page: {layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
	    curr: 1,      // 设置默认起始页1
	    groups: 5,   //只显示10个连续页码,就是说显示10个可见页其他的省略
	    first: false, // 不显示首页
	    last: false   // 不显示尾页
		},
   text:{
	    none:'暂无相关数据'},
	     limit: 20,
		limits: [5,10,15,20,100,1000]
  });
   

  
  		table.on('toolbar(lay0)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            switch(obj.event){
                case 'search': 
                    search(0);
                    break;
            }
        });
        
        table.on('toolbar(lay1)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            switch(obj.event){
                case 'search':               
                    search1(1);
                	break;
        	}
        });
        
        table.on('toolbar(lay2)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            switch(obj.event){
                case 'search':               
                    search2(2);
                	break;
        	}
        });
        
        
        table.on('toolbar(lay3)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            switch(obj.event){
                case 'search':               
                    search3(3);
                	break;
        	}
        });
        

 function search(ptype){
				 	var demoReload = document.getElementById("tag0").value;
	                table.reload('testReload0', {
                    url:"QueryUsecaseInfoSearchServlet2",
                    request: {
                              pageName: 'pageIndex',  // 页码的参数名称，默认：page
                              limitName: 'pageSize', // 每页数据量的参数名，默认：limit
//								search_data:'id'
                      },
                    response: {
                             statusName: 'code',     // 规定数据状态的字段名称，默认：code
                             statusCode: 0,          // 规定成功的状态码，默认：0
                             countName: 'count1', // 规定数据总数的字段名称，默认：count
                             msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                             dataName: 'data1' ,       // 规定数据列表的字段名称，默认：data
                      },
                    page: {
                            layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                            curr: 1,      // 设置默认起始页1
                            groups: 10,   //只显示10个连续页码,就是说显示10个可见页其他的省略
                            first: false, // 不显示首页
                            last: false   // 不显示尾页
                        },
                        limit: 20,
                        limits: [5,10,15,20,100,1000]

                    ,where: {
                        search_data: encodeURI(demoReload),
                        ptype: ptype,
                    }
                  }, 'data1');
                  
             document.getElementById("tag0").value=demoReload;
             }
             
             
      function search1(ptype){
				 	var demoReload = document.getElementById("tag1").value;
//	                console.log(demoReload);
	                table.reload('testReload1', {
                    url:"QueryUsecaseInfoSearchServlet2",
                    request: {
                              pageName: 'pageIndex',  // 页码的参数名称，默认：page
                              limitName: 'pageSize', // 每页数据量的参数名，默认：limit
//								search_data:'id'
                      },
                    response: {
                             statusName: 'code',     // 规定数据状态的字段名称，默认：code
                             statusCode: 0,          // 规定成功的状态码，默认：0
                             countName: 'count1', // 规定数据总数的字段名称，默认：count
                             msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                             dataName: 'data1' ,       // 规定数据列表的字段名称，默认：data
                      },
                    page: {
                            layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                            curr: 1,      // 设置默认起始页1
                            groups: 10,   //只显示10个连续页码,就是说显示10个可见页其他的省略
                            first: false, // 不显示首页
                            last: false   // 不显示尾页
                        },
                        limit: 20,
                        limits: [5,10,15,20,100,1000]

                    ,where: {
                        search_data: encodeURI(demoReload),
                        ptype: ptype,
                    }
                  }, 'data1');
                  
             document.getElementById("tag1").value=demoReload;
             }
             
             
             
              function search2(ptype){
				 	var demoReload = document.getElementById("tag2").value;
//	                console.log(demoReload);
	                table.reload('testReload2', {
                    url:"QueryUsecaseInfoSearchServlet2",
                    request: {
                              pageName: 'pageIndex',  // 页码的参数名称，默认：page
                              limitName: 'pageSize', // 每页数据量的参数名，默认：limit
//								search_data:'id'
                      },
                    response: {
                             statusName: 'code',     // 规定数据状态的字段名称，默认：code
                             statusCode: 0,          // 规定成功的状态码，默认：0
                             countName: 'count1', // 规定数据总数的字段名称，默认：count
                             msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                             dataName: 'data1' ,       // 规定数据列表的字段名称，默认：data
                      },
                    page: {
                            layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                            curr: 1,      // 设置默认起始页1
                            groups: 10,   //只显示10个连续页码,就是说显示10个可见页其他的省略
                            first: false, // 不显示首页
                            last: false   // 不显示尾页
                        },
                        limit: 20,
                        limits: [5,10,15,20,100,1000]

                    ,where: {
                        search_data: encodeURI(demoReload),
                        ptype: ptype,
                    }
                  }, 'data1');
                  
             document.getElementById("tag2").value=demoReload;
             }
             
             
             
              function search3(ptype){
				 	var demoReload = document.getElementById("tag3").value;
	                table.reload('testReload3', {
                    url:"QueryUsecaseInfoSearchServlet2",
                    request: {
                              pageName: 'pageIndex',  // 页码的参数名称，默认：page
                              limitName: 'pageSize', // 每页数据量的参数名，默认：limit
//								search_data:'id'
                      },
                    response: {
                             statusName: 'code',     // 规定数据状态的字段名称，默认：code
                             statusCode: 0,          // 规定成功的状态码，默认：0
                             countName: 'count1', // 规定数据总数的字段名称，默认：count
                             msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                             dataName: 'data1' ,       // 规定数据列表的字段名称，默认：data
                      },
                    page: {
                            layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                            curr: 1,      // 设置默认起始页1
                            groups: 10,   //只显示10个连续页码,就是说显示10个可见页其他的省略
                            first: false, // 不显示首页
                            last: false   // 不显示尾页
                        },
                        limit: 20,
                        limits: [5,10,15,20,100,1000]

                    ,where: {
                        search_data: encodeURI(demoReload),
                        ptype: ptype,
                    }
                  }, 'data1');
                  
             document.getElementById("tag3").value=demoReload;
             }

});
 
 
                      
         
                                                     