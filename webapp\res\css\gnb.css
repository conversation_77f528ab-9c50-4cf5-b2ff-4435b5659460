body{
    width: 100%;
    height: 100%;
    background-color: #06182d;
    background-size: auto;
    background-size: 100% 100%;
    position: relative;
    min-height: 756px;
    padding: 0px;
    margin: 0px;
    font-family: "Microsoft YaHei";
    user-select: none;
}
@font-face{
    font-family: yjsz;
    src:url('../fonts/yjsz.ttf');
}
.t_header{
    width: 100%;
    height: 80px;
    background: url('../images/header.png') no-repeat;
    background-size: auto;
    background-size: 100% 100%;
    position: relative;
}
.t_header span {
    color: #FFFFFF;
    font-size: 30px;
    position: absolute;
    top: 50%;
    margin-top: -20px;
    left: 8.5%;
}
.t_box{
	margin-top: 10px;
}
.t_box1{
	width: 49%;
/* 	height: 500px; */
    display:inline-block;
    position: relative;
    margin: 0 auto;
    margin-left: 1%;
}
.t_box2{
	width: 49%;
/* 	height: 500px; */
    display:inline-block;
    position: relative;
    margin: 0 auto;
    float: right;
    margin-right: 1%;
}

.t_box2 .t_box2_child{
	width: 99%;
    display:inline-block;
    position: relative;
    margin: 0 auto;
}
.t_box3{
	width: 49%;
    display:inline-block;
    position: relative;
    margin: 0 auto;
    margin-left: 1%;
}

.t_mbox div {
	padding-left:10px;
	margin-top:20px;
    font-size: 20px;
    color: #0e94ea;
    line-height: 25px;
    font-weight: 900;
}
.t_mbox1 div {
	padding-left:10px;
	margin-top:5px;
    font-size: 20px;
    color: #0e94ea;
    line-height: 25px;
    font-weight: 900;
}
.t_mbox span {
    font-family: 'yjsz';
    color: #00fbfe;
    text-shadow: 0 0 25px #00fbfe;
    font-weight: bolder;
    font-size: 22px;
}
.t_l_line {
    position: absolute;
    top: 0;
    left: 0;
}
.t_r_line {
    position: absolute;
    bottom: 0;
    right: 0;
}
.t_table_box {
    position: relative;
    width: 98%;
    min-height: 340px;
    display: block;
    text-align: center;
    left: 1%;
}
.table {
    width: 95%;
    margin: 0 auto;
    border-collapse: collapse;
    color: #FFFFFF;
    border-right: 1px solid #0e94ea;
    margin-top: 10px;
}
.table tbody tr td{
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid #0e94ea;
    border-top: 1px solid #0e94ea;
    border-left: 1px solid #0e94ea;
    text-align: center;
    font-size: 14px;
    padding: 2px;
    color: #f2f2f2;
}
.table thead th{
    height: 35px;
    line-height: 35px;
    border-bottom: 1px solid #0e94ea;
    border-top: 1px solid #0e94ea;
    border-left: 1px solid #0e94ea;
    text-align: center;
    background-color: rgba(14, 148, 234, 0.4);
    color: #00b3ac;
    font-weight: bold;
}


.btn-small {
    display: inline-block;
    cursor: pointer;
    font-size: 12px;
    border-radius: 4px;
    background-color: #449d44;
    color: #fff;
    min-width: 40px;
    height: 15px;
    line-height: 15px;
    text-align: center;
    padding: 3px 5px 3px 5px;
}

.btn-small:hover {
    color: #fff;
    background-color: #398439;
}
.btn-red {
    background-color: #d9534f;
}

.btn-red:hover {
    background-color: #d43f3a;
}
.btn-cyan {
    background-color: #59b4c6;
}

.btn-cyan:hover {
    background-color: #59b4c6;
}
input[type="text"] {
    padding: 10px;
    border: 1px solid #04918B;
    background-color: transparent;
    border-radius: .25em;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .08);
    width: 300px;
    color: #FFFFFF;
    position: relative;
/*     left: 35px;	 */
    display:inline-block;
    float: left;
    margin-top: 10px;
    margin-bottom:10px;
    margin-left: 10px;
/*     top: 30px; */
}
#refreshBtn {
    color: #FFFFFF;
    cursor: pointer;
    position: absolute;
    right: 30px;
    top: 30px;
    z-index: 10;
}

.t_nav {
    width: 100%;
    height: 100%;
}
ul, h1, h2, h3, h4, h5, h6, p {
    /*list-style: 0;*/
    padding: 0;
    margin: 0;
}
.t_nav li span {
    font-size: 14px;
    color: #1AA1FD;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    top: 25%;
}
.t_nav li {
    display: inline-block;
    width: 30%;
    height: 100%;
    text-align: center;
    position: relative;
}
#addTj, #updateTj, #deleteTj {
    font-size: 22px;
    color: #fff;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    top: 50%;

}

#bugBtn {
    font-size:14px;
    position: absolute;
    display: none;
    width: 25px;
    height: 25px;
    z-index: 10;
    right: 100px;
    top: 32px;
    background: url('../images/bug.png') no-repeat;
    cursor: pointer;
}
