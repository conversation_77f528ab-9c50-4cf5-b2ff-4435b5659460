<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>水平分类示例 - layui 第三方扩展组件</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="../layui/css/layui.css" media="all">
</head>
<body>

<div style="padding: 50px;">
  <div id="test"></div>
</div>

<script src="../layui/layui.js"></script>
<script>
layui.config({
  base: '../layui_exts/' //配置 layui 第三方扩展组件存放的基础目录
}).extend({
  horizontalClassify: 'horizontalClassify/horizontalClassify'
}).use(['horizontalClassify'], function(){
  var horizontalClassify = layui.horizontalClassify;
  
  //执行示例
  horizontalClassify.render({
    elem: '#test'
    , title : ['一级分类', '二级分类', '三级分类', '四级分类'] // 自定义水平分类标题
    , showSearch: true
    , url : '/demo/horizontalClassify/list'
    , delUrl : '/demo/horizontalClassify/del'
    , addUrl : '/demo/horizontalClassify/add'
    , editUrl : '/demo/horizontalClassify/edit'
  });
});
</script>
</body>
</html>


