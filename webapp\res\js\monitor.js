$(function () {
    $('input').bind('input propertychange', function () {
        $('.commonTable tbody tr').hide()
            .filter(":contains('" + ($(this).val()) + "')").show();
    });

    $("#refreshBtn").click(function () {
        init();
    });
    setInterval(tick, 1000);
});
function tick(){
    var today = new Date();
    document.getElementById("localtime").innerHTML = showLocale(today)+"<font color=\"#ffffff\">  (后台刷新周期:10分钟)</font>";
}
function showLocale(objD){
    var str,colorhead,colorfoot;
    var yy = objD.getYear();
    if(yy<1900) yy = yy+1900;
    var MM = objD.getMonth()+1;
    if(MM<10) MM = '0' + MM;
    var dd = objD.getDate();
    if(dd<10) dd = '0' + dd;
    var hh = objD.getHours();
    if(hh<10) hh = '0' + hh;
    var mm = objD.getMinutes();
    if(mm<10) mm = '0' + mm;
    var ss = objD.getSeconds();
    if(ss<10) ss = '0' + ss;
    var ww = objD.getDay();
    if  ( ww==0 )  colorhead="<font color=\"#ffffff\">";
    if  ( ww > 0 && ww < 6 )  colorhead="<font color=\"#ffffff\">";
    if  ( ww==6 )  colorhead="<font color=\"#ffffff\">";
    if  (ww==0)  ww="星期日";
    if  (ww==1)  ww="星期一";
    if  (ww==2)  ww="星期二";
    if  (ww==3)  ww="星期三";
    if  (ww==4)  ww="星期四";
    if  (ww==5)  ww="星期五";
    if  (ww==6)  ww="星期六";
    colorfoot="</font>"
    str = colorhead + yy + "-" + MM + "-" + dd + " " + hh + ":" + mm + ":" + ss + "  " + ww + colorfoot;
    return(str);
}
var xmlHttp = false;
function createXMLHttpRequest() {
	if (window.ActiveXObject) {
		xmlHttp = new ActiveXObject("Microsoft.XMLHTTP");
	}
	else if (window.XMLHttpRequest) {
		xmlHttp = new XMLHttpRequest();
	}
}
function init() {
	
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = initcallBack;
    var url="QueryNEInfoServlet";  
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);
}
function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data=JSON.parse(data);
			outdevinfo(data[0]);
			devchart(data[1]);
			initTable(data[2]);	
		}
	}
}
function outdevinfo(myData)
{
	document.getElementById("devnum").innerHTML = myData[0].gnbnum;
	document.getElementById("devnumofindoor").innerHTML = myData[0].gnbofindoor;
	document.getElementById("devnumofoutdoor").innerHTML = myData[0].gnbofoutdoor;
	document.getElementById("vsw").innerHTML = myData[0].vswtypenum;
	document.getElementById("vbp").innerHTML = myData[0].vbptypenum;
	document.getElementById("aau").innerHTML = myData[0].aaunum;
	document.getElementById("updatetime").innerHTML = myData[0].updatetime;
	$("#devnum").numberRockInt({
        initNumber: 0,
        lastNumber: myData[0].gnbnum,
        duration: 3000,
        step: 5
    });
}

function initTable(myData) {
    
    var HTML = "<thead>\n" +
        "        <td title=\"序号\">序号</td>\n" +
        "        <td title=\"基站ID\">基站ID</td>\n" +
        "        <td title=\"状态\">状态</td>\n" +
        "        <td title=\"版本\">版本</td>\n" +
        "        <td title=\"主控板\">主控板</td>\n" +
        "        <td title=\"基带板\">基带板</td>\n" +
        "        <td title=\"AAU*RRU\">AAU*RRU</td>\n" +
        "        <td title=\"天线类型\">类型</td>\n" +
        //"        <td title=\"环境位置\">环境位置</td>\n" +
        "        <td title=\"站点类型\">场景</td>\n" +
        "		 <td title=\"责任人\">责任人</td>\n" +
        "		 <td title=\"团队\">团队</td>\n" +
        "		 <td title=\"备注\">备注</td>\n" +
        //"		 <td title=\"更新时间\">更新时间</td>\n" +
        "        </thead>\n" +
        "        <tbody>\n";
        
    $(myData).each(function (index, ele) {
        	HTML += "<tr>\n" +
            "            <td>" + (index + 1) + "</td>\n" +
            "            <td>" + ele['gnbid'] + "</td>\n" ;
            if(ele['connectionState']=="正常")
            {	HTML+="  <td>" + ele['connectionState'] + "</td>\n";}
            else
            { 	HTML+=  "<td><span style='color: #FF0000;'>" + ele['connectionState'] + "</span></td>\n";}
            HTML+=
            "            <td>" + ele['version'] + "</td>\n" +
            "            <td>" + ele['vsw'] + "</td>\n" +
            "            <td>" + ele['vbp'] + "</td>\n" +
            "            <td>" + ele['aau'] + "</td>\n" +
            "            <td>" + ele['aautype']+ "</td>\n" +
            "            <td><span style='color: #00cc00;'>" + ele['scene']+ "</span></td>\n"+
            "            <td><span style='color: #00cc00;'>" + ele['owner']+ "</span></td>\n"+
          
            "            <td><span style='color: #00cc00;'>" + ele['team']+ "</span></td>\n"+
            "            <td><span style='color: #00cc00;'>" + ele['tips']+ "</span></td>\n";
            //"            <td><span style='color: #00cc00;'>" + ele['updatetime']+ "</span></td>\n";
    });
    HTML += "</tbody>";
    $('.commonTable').html(HTML);

    $('.commonTable tbody tr').hide()
        .filter(":contains('" + ($("#searchText").val()) + "')").show();

}

function devchart(mydata)
{
	sumofaau=0;
	for( i=0;i<mydata.length;i++)
		sumofaau+=mydata[i].y
	Highcharts.chart('chart', {
	chart: {
		type: 'column',
		backgroundColor: 'rgba(1,0,0,0)',
	},
	title: {
		text: '2022年5G性能守护团队（西安中移）资源分布',
		style: { color: 'white' },
	},
	subtitle: {
		text: '相关环境详细配置，可登录网管平台查询。当前部署AAU/RRU设备总数：'+sumofaau,
		style: { color: 'white' },
	},
	xAxis: {
		type: 'category',
		labels: { style: { color: 'white' } },
	},
	yAxis: {
		title: {
			text: '机型数量',
			style: { color: 'white' },
		},
		labels: { style: { color: 'white' } },
	},
	legend: {
		enabled: false
	},
	plotOptions: {
		series: {
			borderWidth: 0,
			dataLabels: {
				enabled: true,
				format: '{point.y}'
			}
		}
	},
	tooltip: {
		headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
		pointFormat: '<span style="color:{point.color}">{point.name}</span>: <b>{point.y}</b> of total<br/>'
	},
	series: [{
		name: '机型',
		colorByPoint: true,
		data:mydata
	}]
});
}
