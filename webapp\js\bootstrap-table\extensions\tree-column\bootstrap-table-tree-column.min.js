/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableTreeColumn={exports:{}}.exports}})(this,function(){'use strict';!function(a){a.extend(a.fn.bootstrapTable.defaults,{treeShowField:null,idField:'id',parentIdField:'pid',treeVerticalcls:'vertical',treeVerticalLastcls:'vertical last',treeSpacecls:'space',treeNodecls:'node',treeCellcls:'treenode',treeTextcls:'text',onTreeFormatter:function(a){for(var b=this,c=b.options,d=a._level||0,e=a._parent&&a._parent._level||0,f=[],g=0;g<e;g++)f.push('<i class="'+c.treeVerticalcls+'"></i>'),f.push('<i class="'+c.treeSpacecls+'"></i>');for(var g=e;g<d;g++)a._last&&g===d-1?f.push('<i class="'+c.treeVerticalLastcls+'"></i>'):f.push('<i class="'+c.treeVerticalcls+'"></i>'),f.push('<i class="'+c.treeNodecls+'"></i>');return f.join('')},onGetNodes:function(b,c){var d=this,e=[];return a.each(c,function(a,c){b[d.options.idField]===c[d.options.parentIdField]&&e.push(c)}),e},onCheckLeaf:function(a){return void 0===a.isLeaf?!a._nodes||!a._nodes.length:a.isLeaf},onCheckRoot:function(a){var b=this;return!a[b.options.parentIdField]}});var b=a.fn.bootstrapTable.Constructor,c=b.prototype.initRow,d=b.prototype.initHeader;b.prototype.initHeader=function(){var b=this;d.apply(b,Array.prototype.slice.apply(arguments));var c=b.options.treeShowField;c&&a.each(this.header.fields,function(a,d){if(c===d){b.treeEnable=!0;var e=b.header.formatters[a],f=[b.options.treeCellcls];return b.header.classes[a]&&f.push(b.header.classes[a].split('"')[1]||''),b.header.classes[a]=' class="'+f.join(' ')+'"',b.header.formatters[a]=function(a,c){var d=[b.options.onTreeFormatter.apply(b,[c])];return d.push('<span class="'+b.options.treeTextcls+'">'),e?d.push(e.apply(this,Array.prototype.slice.apply(arguments))):d.push(a),d.push('</span>'),d.join('')},!1}})};var e=function(b,d,f,g){var h=this,j=h.options.onGetNodes.apply(h,[b,f]);b._nodes=j,g.append(c.apply(h,[b,d,f,g]));for(var k,l=j.length-1,m=0;m<=l;m++)k=j[m],k._level=b._level+1,k._parent=b,m===l&&(k._last=1),e.apply(h,[k,a.inArray(k,f),f,g])};b.prototype.initRow=function(a,b,d,f){var g=this;return g.treeEnable?!!g.options.onCheckRoot.apply(g,[a,d])&&(void 0===a._level&&(a._level=0),e.apply(g,[a,b,d,f]),!0):c.apply(g,Array.prototype.slice.apply(arguments))}}(jQuery)});