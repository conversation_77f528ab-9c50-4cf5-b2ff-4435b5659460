/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableKeyEvents={exports:{}}.exports}})(this,function(){'use strict';!function(a){a.extend(a.fn.bootstrapTable.defaults,{keyEvents:!1});var b=a.fn.bootstrapTable.Constructor,c=b.prototype.init;b.prototype.init=function(){c.apply(this,Array.prototype.slice.apply(arguments)),this.initKeyEvents()},b.prototype.initKeyEvents=function(){if(this.options.keyEvents){var b=this;a(document).off('keydown').on('keydown',function(c){var d=b.$toolbar.find('.search input'),e=b.$toolbar.find('button[name="refresh"]'),f=b.$toolbar.find('button[name="toggle"]'),g=b.$toolbar.find('button[name="paginationSwitch"]');if(document.activeElement===d.get(0)||!a.contains(document.activeElement,b.$toolbar.get(0)))return!0;switch(c.keyCode){case 83:return b.options.search?(d.focus(),!1):void 0;case 82:return b.options.showRefresh?(e.click(),!1):void 0;case 84:return b.options.showToggle?(f.click(),!1):void 0;case 80:return b.options.showPaginationSwitch?(g.click(),!1):void 0;case 37:return b.options.pagination?(b.prevPage(),!1):void 0;case 39:return b.options.pagination?void b.nextPage():void 0;}})}}}(jQuery)});