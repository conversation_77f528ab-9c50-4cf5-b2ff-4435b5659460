# vonr3.jsp 文件名校验功能总结

## 功能概述

为vonr3.jsp的语音Log上传功能增加了严格的文件名校验机制，确保上传的WNG文件和Audio文件符合指定的命名规范。

## 校验规则详细说明

### WNG文件命名规则
- **分割符**：使用"-"（横线）
- **最少部分**：至少4个部分
- **第三部分**：必须是"VONR"或"VOLTE"（不区分大小写）
- **第四部分**：必须是"主叫"或"被叫"

**正确示例：**
```
AI智能打卡测试-广州中兴-VONR-主叫.apm
测试项目-北京联通-VOLTE-被叫.dlf
项目名称-地区名称-vonr-主叫.saf
```

**错误示例：**
```
测试-广州-VoNR-主叫.apm        // 第三部分不是VONR/VOLTE
测试-广州-VONR-呼叫.apm        // 第四部分不是主叫/被叫
测试-VONR-主叫.apm            // 部分数量不足
```

### Audio文件命名规则
- **分割符**：使用"_"（下划线）
- **最少部分**：至少3个部分
- **第二部分**：必须是11位数字（电话号码）
- **第三部分**：必须是14位数字（时间格式：YYYYMMDDHHMMSS）
- **时间验证**：年份2020-2030，月份1-12，日期1-31，小时0-23，分钟0-59，秒0-59

**正确示例：**
```
广州中兴_17344224465_20250508110536.m4a
北京测试_13812345678_20241201143022.wav
测试项目_18900000000_20230101000000.mp3
```

**错误示例：**
```
广州中兴_1734422446_20250508110536.m4a    // 电话号码不是11位
广州中兴_17344224465_2025050811053.m4a    // 时间不是14位
广州中兴_17344224465_20250532110536.m4a   // 日期32无效
广州中兴_17344224465_20251301110536.m4a   // 月份13无效
```

## 实现机制

### 1. 实时校验
- 在文件选择时立即进行校验
- 显示详细的错误信息和正确格式示例
- 只有格式正确的文件才会被添加到上传列表

### 2. 上传前校验
- 在点击上传按钮时再次进行校验
- 如果发现格式错误的文件，阻止上传并显示错误列表
- 确保不会上传任何格式错误的文件

### 3. 用户友好的错误提示
- 详细列出所有格式错误的文件名
- 显示正确的命名格式和要求
- 提供具体的错误原因说明

## 技术实现

### JavaScript函数
```javascript
// WNG文件名验证
function validateWngFileName(fileName)

// Audio文件名验证  
function validateAudioFileName(fileName)
```

### 校验时机
1. **文件选择时**：`upload.render()` 的 `choose` 回调函数中
2. **上传前**：`$('#uploadFilesBtn').on('click')` 事件处理函数中

### 错误处理
- 使用Layui的`layer.msg()`显示错误信息
- 错误信息包含HTML格式，支持换行和强调
- 设置较长的显示时间（8-10秒）以便用户阅读

## 测试验证

### 测试文件
- `filename_validator_test.js`：包含完整的测试用例
- 可在浏览器控制台中运行测试
- 覆盖各种正确和错误的命名格式

### 测试用例覆盖
- **WNG文件**：7个测试用例（3个正确，4个错误）
- **Audio文件**：12个测试用例（3个正确，9个错误）
- 涵盖各种边界情况和错误类型

### 运行测试
```javascript
// 在浏览器控制台中运行
runFileNameValidationTests();
```

## 用户体验改进

### 1. 界面提示
- 在文件选择区域显示详细的命名格式要求
- 使用不同颜色区分正确和错误的示例
- 提供具体的格式说明

### 2. 实时反馈
- 文件选择后立即显示校验结果
- 错误文件不会被添加到上传列表
- 文件计数只包含格式正确的文件

### 3. 防错机制
- 多重校验确保不会上传错误格式的文件
- 详细的错误信息帮助用户快速定位问题
- 阻止式校验避免无效上传

## 配置和维护

### 扩展校验规则
如需修改校验规则，可以调整以下部分：
1. 正则表达式模式
2. 时间范围验证
3. 分割符和部分数量要求

### 错误信息定制
可以在以下位置修改错误提示信息：
1. 文件选择时的错误提示
2. 上传前的错误提示
3. 页面上的格式说明

## 文件清单

### 主要文件
- `vonr3.jsp`：包含文件名格式说明的主页面
- `vonrinfo3.js`：包含校验逻辑的JavaScript文件

### 辅助文件
- `filename_validator_test.js`：测试脚本
- `test_vonr3.html`：测试页面
- `vonr3_readme.md`：详细说明文档
- `vonr3_filename_validation_summary.md`：本总结文档

## 总结

文件名校验功能的添加显著提高了系统的数据质量和用户体验：

1. **数据质量**：确保所有上传的文件都符合命名规范
2. **用户体验**：提供清晰的错误提示和格式说明
3. **系统稳定性**：防止因文件名格式错误导致的后续处理问题
4. **可维护性**：模块化的校验函数便于维护和扩展

该功能已经过充分测试，可以投入生产环境使用。
