/**
 * 文件名校验测试函数
 * 用于测试vonrinfo3.js中的文件名校验功能
 */

// WNG文件名验证函数（复制自vonrinfo3.js）
function validateWngFileName(fileName) {
    // 移除文件扩展名
    var nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
    
    // 以"-"分割文件名
    var parts = nameWithoutExt.split('-');
    
    // 检查是否至少有4个部分
    if (parts.length < 4) {
        return false;
    }
    
    // 第三个部分应该是VONR或VOLTE
    var thirdPart = parts[2].trim().toUpperCase();
    if (thirdPart !== 'VONR' && thirdPart !== 'VOLTE') {
        return false;
    }
    
    // 第四个部分应该是主叫或被叫
    var fourthPart = parts[3].trim();
    if (fourthPart !== '主叫' && fourthPart !== '被叫') {
        return false;
    }
    
    return true;
}

// Audio文件名验证函数（复制自vonrinfo3.js）
function validateAudioFileName(fileName) {
    // 移除文件扩展名
    var nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
    
    // 以"_"分割文件名
    var parts = nameWithoutExt.split('_');
    
    // 检查是否至少有3个部分
    if (parts.length < 3) {
        return false;
    }
    
    // 第二个部分应该是电话号码（11位数字）
    var phonePart = parts[1].trim();
    if (!/^\d{11}$/.test(phonePart)) {
        return false;
    }
    
    // 第三个部分应该是时间格式（14位数字，格式：YYYYMMDDHHMMSS）
    var timePart = parts[2].trim();
    if (!/^\d{14}$/.test(timePart)) {
        return false;
    }
    
    // 验证时间格式的合理性
    var year = parseInt(timePart.substring(0, 4));
    var month = parseInt(timePart.substring(4, 6));
    var day = parseInt(timePart.substring(6, 8));
    var hour = parseInt(timePart.substring(8, 10));
    var minute = parseInt(timePart.substring(10, 12));
    var second = parseInt(timePart.substring(12, 14));
    
    if (year < 2020 || year > 2030 || 
        month < 1 || month > 12 || 
        day < 1 || day > 31 || 
        hour < 0 || hour > 23 || 
        minute < 0 || minute > 59 || 
        second < 0 || second > 59) {
        return false;
    }
    
    return true;
}

// 测试用例
function runFileNameValidationTests() {
    console.log('=== 文件名校验测试开始 ===');
    
    // WNG文件测试用例
    console.log('\n--- WNG文件测试 ---');
    var wngTestCases = [
        { name: 'AI智能打卡测试-广州中兴-VONR-主叫.apm', expected: true },
        { name: '测试项目-北京联通-VOLTE-被叫.dlf', expected: true },
        { name: '项目-地区-vonr-主叫.saf', expected: true }, // 测试大小写不敏感
        { name: '测试-广州-VoNR-主叫.apm', expected: false }, // 第三部分错误
        { name: '测试-广州-VONR-呼叫.apm', expected: false }, // 第四部分错误
        { name: '测试-VONR-主叫.apm', expected: false }, // 部分不足
        { name: '测试.apm', expected: false } // 部分严重不足
    ];
    
    wngTestCases.forEach(function(testCase) {
        var result = validateWngFileName(testCase.name);
        var status = result === testCase.expected ? '✅ PASS' : '❌ FAIL';
        console.log(status + ' - ' + testCase.name + ' (期望: ' + testCase.expected + ', 实际: ' + result + ')');
    });
    
    // Audio文件测试用例
    console.log('\n--- Audio文件测试 ---');
    var audioTestCases = [
        { name: '广州中兴_17344224465_20250508110536.m4a', expected: true },
        { name: '北京测试_13812345678_20241201143022.wav', expected: true },
        { name: '测试_18900000000_20230101000000.mp3', expected: true },
        { name: '广州中兴_1734422446_20250508110536.m4a', expected: false }, // 电话号码不是11位
        { name: '广州中兴_17344224465_2025050811053.m4a', expected: false }, // 时间不是14位
        { name: '广州中兴_17344224465_20250532110536.m4a', expected: false }, // 日期32无效
        { name: '广州中兴_17344224465_20251301110536.m4a', expected: false }, // 月份13无效
        { name: '广州中兴_17344224465_20250508250536.m4a', expected: false }, // 小时25无效
        { name: '广州中兴_17344224465_20250508116536.m4a', expected: false }, // 分钟65无效
        { name: '广州中兴_17344224465_20250508110566.m4a', expected: false }, // 秒66无效
        { name: '广州中兴_17344224465.m4a', expected: false }, // 部分不足
        { name: '测试.m4a', expected: false } // 部分严重不足
    ];
    
    audioTestCases.forEach(function(testCase) {
        var result = validateAudioFileName(testCase.name);
        var status = result === testCase.expected ? '✅ PASS' : '❌ FAIL';
        console.log(status + ' - ' + testCase.name + ' (期望: ' + testCase.expected + ', 实际: ' + result + ')');
    });
    
    console.log('\n=== 文件名校验测试结束 ===');
}

// 在页面加载完成后运行测试
if (typeof window !== 'undefined') {
    window.runFileNameValidationTests = runFileNameValidationTests;
    console.log('文件名校验测试函数已加载，请在控制台运行 runFileNameValidationTests() 来执行测试');
} else {
    // Node.js环境下直接运行
    runFileNameValidationTests();
}
