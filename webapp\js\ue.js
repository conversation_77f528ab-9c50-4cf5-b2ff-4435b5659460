var xmlHttp=false; 
//console.log( parent.myjson);
//console.log( eval('('+parent.myjson+')'));
//var ttttttt = eval('('+parent.myjson+')');
//alert(ttttttt.taskid)
//alert(ttttttt.prefix)
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}
	

function init(){
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = initcallBack;
    	var url="QueryUeInfoServlet";  
    	xmlHttp.open("GET", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);	
}

function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			table0(data[0]);
//			getSessionData()
		}
	}	
}

//function getSessionData() {
//  var data = sessionStorage.getItem("session_data");
//  if (data) {
//	console.log(data);
//    // 在页面上显示Session数据
//    // ...
//  }
//}

function table0(data){
	var tbody  = document.getElementById("tbody") ; 
	s="";
	for(var i=0;i<data.length;i++){
		s+="<td style='text-align:center' >"+(i+1)+"</td>";
		s+="<td title='"+data[i].proxyip+"' style='overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].proxyip+"</td>";
		s+="<td title='"+data[i].mcpip+"' style='overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].mcpip+"</td>";
		s+="<td title='"+data[i].uenumber+"' style='overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].uenumber+"</td>";
		s+="<td title='"+data[i].brand+"' style='overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].brand+"</td>";
		s+="<td title='"+data[i].type+"' style='overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].type+"</td>";	
		if(data[i].status==0){
			s+="<td title='接入' style='color:green'>"+"接入"+"</td>";
		}else{
			s+="<td title='飞行' style='color:red'>"+"飞行"+"</td>";
		}
		if(data[i].ipaddr==null){
			s+="<td >"+""+"</td>";
		}else{
			s+="<td title='"+data[i].ipaddr+"' style='overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].ipaddr+"</td>";
		}
		if(data[i].cell==null){
			s+="<td >"+""+"</td>";
		}else{
			s+="<td title='"+data[i].cell+"' style='overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].cell+"</td>";
		}
		if(data[i].rsrp==null){
			s+="<td >"+""+"</td>";
		}else{
			s+="<td title='"+data[i].rsrp+"' style='overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].rsrp+"</td>";
		}
		if(data[i].sinr==null){
			s+="<td >"+""+"</td>";
		}else{
			s+="<td title='"+data[i].sinr+"' style='overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].sinr+"</td>";
		}
		s+="<td title='"+data[i].updatetime+"' style='overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].updatetime+"</td>";
//		s+="<td >"+"<button class='layui-btn layui-btn-xs' onclick='fly()'>飞行</button>&nbsp;&nbsp<button class='layui-btn layui-btn-xs' onclick='unfly()'>去飞行</button></td>";
		s+="</tr>"
	}
	tbody.innerHTML=s;
}

function tap0(){
	var id  = document.getElementById("ueinfo").value ;
	if(id==""){
		alert("请输入代理IP")
	}else{
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = tap0callBack;
    	var url="QueryUeInfobyidentificationServlet?id="+id;  
    	xmlHttp.open("GET", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);
	}
}

function tap0callBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
            table0(data[0]);
           }
        } 
}

function fly()
{
	var td = event.srcElement;
	tr = td.parentElement.parentElement;
	var ue=tr.cells[4].innerText;
	alert(ue+"飞行");
}

function unfly()
{
		var td = event.srcElement;
	tr = td.parentElement.parentElement;
	var ue=tr.cells[4].innerText;
	alert(ue+"去飞行");
}

function refresh(){
		var id  = document.getElementById("ueinfo2").value ;
		if(id==""){
			alert("请输入代理IP")
		}else{
//			createXMLHttpRequest();
//	    	xmlHttp.onreadystatechange = refreshcallBack;
//	    	var url="QueryUeRefreshInfoServlet?id="+id;  
//	    	xmlHttp.open("GET", url, true);
//	    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
//	    	xmlHttp.send(null);
		
          layer.confirm('请确认您的代理和MCP链接状态正常', function(index){
                 $.ajax({
             		url : 'QueryUeRefreshInfoServlet',
             		type : 'get',
             		dataType : 'json',
             		data :{
             			"id":id,
//         				"oagentip":oagentip,
//         				"oagentname":encodeURI(oagentname),
//         				"omcpip":omcpip,
//             			"ohead":encodeURI(ohead),
//             			"osubteam":encodeURI(osubteam),
             		},
             		beforeSend:function(){
						i=showLoad();
					},
             		success: function(result){
                         if(result.code == 0){
                             layer.close(i);
//                             layer.close(index);
                             init();
                         }
                     }
             	});
             	
             	function showLoad(){
					return layer.msg('拼命执行中..',{
						time:10000000
						
					})
				}
				function closeLoad(index1){
					layer.close(index1);
				}
            });
            
            
	
	}

}

function refreshcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			init();
		}
	}	
}



