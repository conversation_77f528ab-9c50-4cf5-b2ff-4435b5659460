.layui-view-body{
    background-color: #f0f2f5;
}

.layui-content{
    padding: 20px;
}

.layui-tab-title{
    border-bottom-color: #e8e8e8;
}
.layui-card .layui-tab-brief .layui-tab-title li{
    margin:0 15px;
    padding: 0;
}

.layui-form-checked i, .layui-form-checked:hover i,
.layui-form-radio>i:hover, .layui-form-radioed>i,
.layui-breadcrumb a:hover,
.layui-laypage a:hover,
.layui-tab-brief>.layui-tab-title .layui-this{
    color: #177ce3!important;
}

.layui-btn-primary:hover,
.layui-form-onswitch,
.layui-form-checked[lay-skin=primary] i,
.layui-form-checkbox[lay-skin=primary]:hover i,
.layui-form-checked, .layui-form-checked:hover,
.layui-tab-brief>.layui-tab-more li.layui-this:after,
.layui-tab-brief>.layui-tab-title .layui-this:after{
    border-color: #177ce3;
}

.layui-checkbox-disbaled[lay-skin=primary]:hover i {
    border-color: #d2d2d2!important;
}

.layui-form-onswitch,
.layui-form-checked[lay-skin=primary] i,
.layui-form-select dl dd.layui-this,
.layui-laypage .layui-laypage-curr .layui-laypage-em,
.layui-form-checked span, .layui-form-checked:hover span{
    background-color: #177ce3;
}
.layui-btn-blue{
    background-color: #177ce3;
    background-repeat: repeat-y;
    background-image: -moz-linear-gradient(left,#29adeb,#177ce3);
    background-image: -webkit-linear-gradient(left,#29adeb,#177ce3);
    background-image: -o-linear-gradient(left,#29adeb,#177ce3);
    background-image: linear-gradient(left,#29adeb,#177ce3);
}

.layui-form-checkbox[lay-skin=primary]:hover span{
    background: 0 0!important;
}

.layui-page-header{
    margin: -20px -20px 20px;
}

.layui-page-header .pagewrap{
    padding: 15px 20px;
    background-color: #fff;
}

.layui-page-header .title{
    margin-top: 15px;
}

.chart-card{
    padding: 20px 24px 8px;
}

.chart-card .chart-header{
    position: relative;
    width: 100%;
    overflow: hidden;
}

.chart-card .metawrap{
    float: center;
}

.chart-card .metawrap .meta{
    color: rgba(0,0,0,.45);
    font-size: 14px;
    line-height: 22px;
    height: 22px;
}

.chart-card .metawrap .total{
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap;
    color: rgba(0,0,0,.85);
    margin-top: 4px;
    margin-bottom: 0;
    font-size: 30px;
    line-height: 38px;
    height: 38px;
}

.chart-card .chart-body{
    margin-bottom: 12px;
    position: relative;
    width: 100%;
}

.chart-card .chart-footer{
    padding-top: 9px;
    margin-top: 8px;
    border-top: 1px solid #e8e8e8;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.chart-card .field{

}

.chart-card .field span{
    font-size: 14px;
    line-height: 22px;
}

.chart-card .field span:last-child{
    margin-left: 8px;
    color: rgba(0,0,0,.85);
}

.form-box{
    padding: 10px 0;
}