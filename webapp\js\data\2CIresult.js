var xmlHttp=false;  

 function createXMLHttpRequest() {
     if (window.ActiveXObject){
           xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
     }
     else if (window.XMLHttpRequest){
           xmlHttp = new XMLHttpRequest();
      }  
 }

var v;    
function init(version) 
{	
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = initcallBack;
    var url="Query1CITotalInfoServlet?version="+version;  
    v=version;
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}

function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
            var s  =  document.getElementById("version"); 
        	for(var i=0;i<data[0].length;i++){
        	   var option  =   new  Option(data[0][i],i);   
        	   s.options[i] = option;
			} 
			table1(data[1],v); 
			table2(data[1],v); 
			table3(data[2]); 
            }
     }
}

function table3(data){
	var tbody3  = document.getElementById("tbody3") ; 
	s="";
	
	s+="<tr>";
	for(var i=0;i<data.length;i++){
		s+="<td style='text-align:center'>"+data[i]+"</td>";
	}
	s+="<td style='text-align:center;cursor:pointer;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+"<p ondblclick='choose3()'>查看</p>"+"</td>";
	s+="</tr>"
	tbody3.innerHTML=s;	
}

function table1(data,v){
	var tbody1  = document.getElementById("tbody1") ; 
	s="";
	for(var i=0;i<data.length;i++){
		//var a = "\"choose("+data[i].devName+")\"";
		if(data[i].ifpass=="不通过"){
			s+="<tr bgcolor='#ef7a82'>";
		}
		else{
			s+="<tr>";
		}
		s+="<td title='"+v+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+v+"</td>";
		s+="<td title='"+data[i].contrastTestVersion+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].contrastTestVersion+"</td>";
		s+="<td title='"+data[i].team+"' ondblclick='classify(1)' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].team+"</td>";
		s+="<td title='"+data[i].gnbid+"' ondblclick='classify(2)' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].gnbid+"</td>";
		s+="<td title='"+data[i].aautype+"' ondblclick='classify(3)' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].aautype+"</td>";
		s+="<td title='"+data[i].devName+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].devName+"</td>";
		s+="<td title='"+data[i].score+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].score+"</td>";
		s+="<td title='"+data[i].improve+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].improve+"</td>";
		s+="<td title='"+data[i].normal+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].normal+"</td>";
		s+="<td title='"+data[i].bad+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].bad+"</td>";
		s+="<td title='"+data[i].ifpass+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i].ifpass+"</td>";
		s+="<td title='查看' style='text-align:center;cursor:pointer;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+"<p ondblclick='choose()'>查看</p>"+"</td>";
		s+="</tr>"
	}
	tbody1.innerHTML=s;	
}

function table2(data,v){
	var tbody1  = document.getElementById("tbody2") ; 
	var data2=[];
	for(var i=0;i<data.length;i++){
		if(data[i].ifpass2=="不通过"){
			data2.push(data[i]);
		}
	}
	for(var i=0;i<data.length;i++){
		if(data[i].ifpass2=="待复测,一票否决"){
			data2.push(data[i]);
		}
	}
	for(var i=0;i<data.length;i++){
		if(data[i].ifpass2=="待复测,用户数少于15"){
			data2.push(data[i]);
		}
	}
	for(var i=0;i<data.length;i++){
		if(data[i].ifpass2=="通过"){
			data2.push(data[i]);
		}
	}
	
	s="";
	for(var i=0;i<data2.length;i++){
		//var a = "\"choose("+data[i].devName+")\"";
		if(data2[i].ifpass2=="不通过"){
			s+="<tr bgcolor='#ef7a82'>";
		}
		else{
			s+="<tr>";
		}
		s+="<td title='"+v+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+v+"</td>";
		s+="<td title='"+"历史平均"+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+"历史平均"+"</td>";
		s+="<td title='"+data2[i].team+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data2[i].team+"</td>";
		s+="<td title='"+data2[i].gnbid+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data2[i].gnbid+"</td>";
		s+="<td title='"+data2[i].aautype+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data2[i].aautype+"</td>";
		s+="<td title='"+data2[i].devName+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data2[i].devName+"</td>";
		if(data2[i].score2==undefined){
			s+="<td style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+""+"</td>";
			s+="<td style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+""+"</td>";
			s+="<td style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+""+"</td>";
			s+="<td style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+""+"</td>";
			s+="<td style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+""+"</td>";
			s+="<td style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+""+"</td>";
		}else{
			s+="<td title='"+data2[i].score2+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data2[i].score2+"</td>";
			s+="<td title='"+data2[i].improve2+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data2[i].improve2+"</td>";
			s+="<td title='"+data2[i].normal2+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data2[i].normal2+"</td>";
			s+="<td title='"+data2[i].bad2+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data2[i].bad2+"</td>";
			s+="<td title='"+data2[i].ifpass2+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data2[i].ifpass2+"</td>";
			s+="<td style='text-align:center;cursor:pointer;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+"<p ondblclick='choose2()'>查看</p>"+"</td>";
		}
		s+="</tr>"
	}
	tbody1.innerHTML=s;	
}

function classify(index){
	var td = event.srcElement;
	var content = td.innerText;
	content=encodeURIComponent(content);
	content=encodeURIComponent(content);
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = classifycallBack;
    var url="Query1CIClassifyInfoServlet?content="+content+"&index="+index+"&version="+v;  
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}

function classifycallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
        	addrow(data[0]);
            }
     }
}

function addrow(data){
	var tr = document.createElement("tr");
	for(var i=0;i<data.length;i++){
		var td1 = document.createElement("td");
//		td1.style="text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;";
//		td1.title=data[i];
//		td1.innerText=data[i];
		var s="<td title='"+data[i]+"' style='text-align:center;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;'>"+data[i]+"</td>";
		td1.innerHTML=s;
		tr.appendChild(td1);
	}
	var td = document.createElement("td");
	td.innerHTML="<p ondblclick='choose3()'>查看</p>";
	tr.appendChild(td);
	document.getElementById("tbody3").appendChild(tr);
}


function  chooseversion(){
	var version=null; var s  =  document.getElementById( "version" ); 
	version=s.options[s.selectedIndex].text;
	window.open("1CIversion.jsp?version="+version,"_self");
}

function choose(){
	var td = event.srcElement;
	tr =td.parentElement.parentElement; 
	var dev=tr.cells[5].innerHTML;
	var contrastTestVersion = tr.cells[1].innerHTML;
	var team=tr.cells[2].innerHTML;
	team=encodeURIComponent(team);
	team=encodeURIComponent(team);
	window.open("kpi.jsp?version="+v+"&devname="+dev+"&contrastTestVersion="+contrastTestVersion+"&team="+team+"&index="+1);
}

function choose2(){
	var td = event.srcElement;
	tr =td.parentElement.parentElement; 
	var dev=tr.cells[5].innerHTML;
	var contrastTestVersion = tr.cells[1].innerHTML;
	var team=tr.cells[2].innerHTML;
	team=encodeURIComponent(team);
	team=encodeURIComponent(team);
	window.open("kpi.jsp?version="+v+"&devname="+dev+"&contrastTestVersion="+contrastTestVersion+"&team="+team+"&index="+2);
}

function choose3(){
	var td = event.srcElement;
	tr =td.parentElement.parentElement; 
	var dev="";
	var contrastTestVersion = tr.cells[1].innerHTML;
	var team=tr.cells[3].innerText;
	var index=Number(tr.cells[2].innerText)+3;
	team=encodeURIComponent(team);
	team=encodeURIComponent(team);
	window.open("kpi.jsp?version="+v+"&devname="+dev+"&contrastTestVersion="+contrastTestVersion+"&team="+team+"&index="+index);
}



function tap(){
	document.getElementById('judge1').style.display="block";
	document.getElementById('judge2').style.display="none";
//	document.getElementById('way').innerText="上一版对比";
}
function tap2(){
	document.getElementById('judge1').style.display="none";
	document.getElementById('judge2').style.display="block";
//	document.getElementById('way').innerHTML="历史平均对比";
}
function tap3(){
	window.open("1CIMetrics.jsp?version="+v,"_self");
}

function back(){
	window.open("datapanel.jsp","_self");
}
