var xmlHttp=false;  
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}
function init(){
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = initcallBack;
    	var url="QueryStaServlet";  
    	xmlHttp.open("POST", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);	
}
function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data);
            chart(data[0]);	
		}
	}	
}

function chart(data){
	var s ="";
	for(var i = 0;i<data.length;i++){
		s+="<tr>"
		if(data[i].assetno==null){
			s+="<td>"+""+"</td>";
		}else{
			s+="<td>"+data[i].assetno+"</td>";
		}
		if(data[i].ip==null){
			s+="<td>"+""+"</td>";
		}else{
			s+="<td>"+data[i].ip+"</td>";
		}
		if(data[i].serverip==null){
			s+="<td>"+""+"</td>";
		}else{
			s+="<td>"+data[i].serverip+"</td>";
		}
		if(data[i].deptFullName==null){
			s+="<td>"+""+"</td>";
		}else{
			s+="<td>"+data[i].deptFullName+"</td>";
		}
		if(data[i].owner==null){
			s+="<td>"+""+"</td>";
		}else{
			s+="<td>"+data[i].owner+"</td>";
		}
		

		if(data[i].xjtesthelperappversion==null){
			s+="<td>"+""+"</td>";
		}else{
			s+="<td>"+data[i].xjtesthelperappversion+"</td>";
		}
		if(data[i].restatus==null){
			s+="<td>"+""+"</td>";
		}else{
			s+="<td>"+data[i].restatus+"</td>";
		}
		
		if(data[i].updatetime==null){
			s+="<td>"+""+"</td>";
		}else{
			s+="<td>"+data[i].updatetime+"</td>";
		}
		
		s+="</tr>"
	}
	document.getElementById("tbody1").innerHTML = s;	
}


function initTable(myData) {
    
    var HTML = "";
        
    $(myData).each(function (index, ele) {
        	HTML += "<tr>\n" +
            "            <td>" + ele['assetno'] + "</td>\n"+
            "            <td>" + ele['ip'] + "</td>\n"+
            "            <td>" + ele['owner'] + "</td>\n"+
            "            <td>" + ele['area'] + "</td>\n"+
            "            <td>" + ele['restatus'] + "</td>\n"+ 
            "            <td>" + ele['updatetime'] + "</td>\n"+ 
            "            </tr>\n" ;
    });
    $('#tbody1').html(HTML);
}





