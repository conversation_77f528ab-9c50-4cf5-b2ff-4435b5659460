var xmlHttp=false;  
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}

function step1(){
	var type=null; var s  =  document.getElementById("tasktype"); 
//	type=s.options[s.selectedIndex].text;
	type=s.options[s.selectedIndex].value;
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = stepcallBack;
	var url=encodeURI("TaskCreateEnvInfoServlet?ptype="+(type-1));  
	xmlHttp.open("GET", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);	
}

function stepcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			table(data[0]);
	}	
}
}


function step2(){
	// 提交角色菜单
	layui.use(['tree'],function(){
		var tree = layui.tree;
		var checkData = tree.getChecked('demoId1');
    	var mids = [];
    	getTreeValue(checkData, mids);// 获取树形选中值
		alert(mids);
	})

}


function getTreeValue(list, mids){
	$(list).each(function (i, o) {
		if(o.level!=1){
			    	mids.push(o.title);
		}
    	getTreeValue(o.children, mids);
    });
}

function table(data1){
//	var d =$("#table1 tr").length;
//	var tbody  = document.getElementById("tbody1") ; 
//	s="";
//	s+="<td style='text-align:center' >"+d+"</td>";
//	s+="<td >"+"<div class='layui-form-item'><div id='"+d+"11"+"' class='demo-tree-more'></div></div>"+"</td>";
//	s+="<td >"+"<div class='layui-form-item'><div id='"+d+"12"+"' class='demo-tree-more'></div></div>"+"</td>";
//	s+="<td id='"+d+"13'>"+""+"</td>";
//	s+="<td id='"+d+"14'>"+""+"</td>";
//	s+="<td >"+"<button class='layui-btn layui-btn-xs' onclick='clean()'>清空</button></td>";
//	s+="</tr>"
//	tbody.innerHTML=s;
//	divid = d+"11";
//	divid2 = d+"12";

	layui.use(['tree', 'util'], function(){
		var tree = layui.tree
		  ,layer = layui.layer
		   ,util = layui.util
		  //模拟数据
  
  //基本演示
  tree.render({
    elem: "#test1"
    ,data: data1
    ,showCheckbox: true  //是否显示复选框
    ,id: 'demoId1'
	,accordion: true 
    ,isJump: true //是否允许点击节点时弹出新窗口跳转
    ,click: function(obj){
		if(obj.data.level==2){
			document.getElementById("113").innerText=obj.data.title;
		}
    }
  });
  
  //按钮事件
  util.event('lay-demo', {
    getChecked: function(othis){
      var checkedData = tree.getChecked('demoId1'); //获取选中节点的数据     
      layer.alert(JSON.stringify(checkedData), {shade:0});
      console.log(checkedData);
    }
    ,setChecked: function(){
      tree.setChecked('demoId1', [12, 16]); //勾选指定节点
    }
    ,reload: function(){
      //重载实例
      tree.reload('demoId1', {       
      });
      
    }
  });
  
//    //基本演示
//  tree.render({
//    elem: "#"+divid2+""
//    ,data: data2
//    ,showCheckbox: false  //是否显示复选框
//    ,id: 'demoId2'
//	,accordion: true 
//    ,isJump: true //是否允许点击节点时弹出新窗口跳转
//    ,click: function(obj){
//      	if(obj.data.level==2){
//			t = document.getElementById("114").innerHTML;
//			document.getElementById("114").innerHTML=t + "<br>"+obj.data.title;
//		}
//    }
//  });
  
//  //按钮事件
//  util.event('lay-demo', {
//    getChecked: function(othis){
//      var checkedData = tree.getChecked('demoId2'); //获取选中节点的数据     
//      layer.alert(JSON.stringify(checkedData), {shade:0});
//      console.log(checkedData);
//    }
//    ,setChecked: function(){
//      tree.setChecked('demoId2', [12, 16]); //勾选指定节点
//    }
//    ,reload: function(){
//      //重载实例
//      tree.reload('demoId2', {       
//      });
//      
//    }
//  });
});
}


 function clean()
{
	var td = event.srcElement;
	tr = td.parentElement.parentElement;
	var id=tr.cells[0].innerText;
	
}

function printObject(obj){ 
//obj = {"cid":"C0","ctext":"区县"}; 
var temp = ""; 
for(var i in obj){//用javascript的for/in循环遍历对象的属性 
temp += i+":"+obj[i]+"\n"; 
} 
alert(temp);//结果：cid:C0 \n ctext:区县 
} 


function tap2(){
	var id  = document.getElementById("usecase2").value ; 
	if(id==""){
		alert("请输入用例标识")
	}else{
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = tap2callBack;
    	var url="QueryUsecaseInfobyidServlet?id="+id+"&ptype="+2;  
    	xmlHttp.open("GET", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);
	}
}

function tap2callBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
            data=JSON.parse(data); 
            console.log("data",data);
            table2(data[0]);
           }
        } 
}


