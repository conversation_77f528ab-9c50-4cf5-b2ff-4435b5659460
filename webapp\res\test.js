var xmlHttp=false;      
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}
 
function xjrequest()
{
	createXMLHttpRequest();
    xmlHttp.onreadystatechange = null;
    var url="QueryHWInfoServlet";  
    xmlHttp.open("POST", url, true);
    xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    xmlHttp.send(null);
    alert("您点击了按钮");
}