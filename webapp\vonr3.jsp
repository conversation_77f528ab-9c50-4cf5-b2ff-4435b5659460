<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>iWork自动化测试 - 语音Log上传</title>
<meta name="author" content="xuejie10170365">
<link href="css/bootstrap.min.css" rel="stylesheet">
<link href="css/materialdesignicons.min.css" rel="stylesheet">
<link href="css/style.min.css" rel="stylesheet">
<link href="addtask/layui-v2.8.3/layui/css/layui.css" type="text/css" rel="stylesheet">
<link href="css/layer/layer.css" type="text/css" rel="stylesheet">

</head>
<style>
	.collapsible-content thead {
    display: none;
}
.collapsible-content {
    display: none;
}

/* 文件上传页面优化样式 */
.upload-section {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.upload-section:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.file-select-group {
    transition: all 0.3s ease;
}

.file-select-group:hover {
    border-color: #5FB878 !important;
    box-shadow: 0 2px 8px rgba(95,184,120,0.2);
}

.result-section {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.layui-btn-radius {
    border-radius: 20px;
}

.layui-btn-lg {
    padding: 12px 30px;
    font-size: 16px;
}

/* 表格区域优化 */
.layui-table-view {
    margin-top: 0;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .layui-tab-item {
        padding: 15px !important;
    }
    
    .upload-section, .result-section {
        padding: 20px !important;
        margin-bottom: 20px !important;
    }
    
    .file-select-group {
        padding: 12px !important;
    }
    
    .upload-action {
        padding: 15px !important;
    }
}

/* 文件计数样式优化 */
#wngFileCount, #audioFileCount {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    background: #f0f0f0;
    font-size: 12px;
}

#wngFileCount.selected, #audioFileCount.selected {
    background: #e8f5e8;
    color: #5FB878;
}
</style>
<body>
<div class="container-fluid p-t-15">

  <div class="row">
    <div class="col-md-12">
      <div class="card">

      <center style="padding-top: 20px">
              <a style="font-size: 30px;font-weight: 500;">AI智能语音分析看板<span class="layui-badge layui-bg-blue">iWork</span></a>        <div class="card-body">

      </center>
      				<a onclick="tap1()" style="display:inline-block;float:right;margin-right: 10px" class="layui-btn layui-btn-sm layui-btn-radius layui-btn-normal" data-animation="fadeInUp" data-delay=".6s">测试工具箱(公网)</a>

          <!-- Tab导航 -->
          <div class="layui-tab layui-tab-brief" lay-filter="mainTab">
            <ul class="layui-tab-title">
              <li class="layui-this">语音分析记录</li>
              <li>语音Log上传</li>
            </ul>
            <div class="layui-tab-content">
              <!-- 第一个tab：语音分析记录 -->
              <div class="layui-tab-item layui-show">
                <table id="table0" lay-filter="lay0" class="layui-table" style="table-layout:fixed;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;">
                </table>
              </div>
              <!-- 第二个tab：语音Log上传 -->
              <div class="layui-tab-item" style="padding: 20px;">
                
                <!-- 文件上传区域 -->
                <div class="upload-section" style="background: #f8f9fa; border-radius: 8px; padding: 25px; margin-bottom: 30px; border: 1px solid #e9ecef;">
                  <div style="font-size: 18px; font-weight: bold; margin-bottom: 20px; color: #333; border-bottom: 2px solid #5FB878; padding-bottom: 10px;">
                    <i class="layui-icon layui-icon-upload" style="color: #5FB878;"></i> 语音Log文件上传
                  </div>
                  
                  <div class="upload-controls" style="margin-bottom: 25px;">
                    
                    <!-- WNG文件选择 -->
                    <div class="file-select-group" style="margin-bottom: 20px; padding: 15px; background: #fff; border-radius: 6px; border: 1px solid #ddd;">
                      <div style="margin-bottom: 10px;">
                        <button type="button" class="layui-btn layui-btn-normal layui-btn-radius" id="selectWngFilesBtn">
                          <i class="layui-icon layui-icon-upload"></i> 选择WNG文件
                        </button>
                        <span id="wngFileCount" style="margin-left: 15px; color: #666; font-weight: 500;">未选择文件</span>
                      </div>
                      <div style="color: #999; font-size: 12px; margin-left: 5px;">
                        支持.apm、.dlf、.saf格式，可多选。参考命名：AI智能打卡测试-广州中兴-VONR-主叫.apm
                      </div>
                    </div>
                    
                    <!-- Audio文件选择 -->
                    <div class="file-select-group" style="margin-bottom: 20px; padding: 15px; background: #fff; border-radius: 6px; border: 1px solid #ddd;">
                      <div style="margin-bottom: 10px;">
                        <button type="button" class="layui-btn layui-btn-normal layui-btn-radius" id="selectAudioFilesBtn">
                          <i class="layui-icon layui-icon-upload"></i> 选择Audio文件
                        </button>
                        <span id="audioFileCount" style="margin-left: 15px; color: #666; font-weight: 500;">未选择文件</span>
                      </div>
                      <div style="color: #999; font-size: 12px; margin-left: 5px;">
                        支持音频、视频格式。参考命名：广州中兴_17344224465_20250508110536.m4a
                      </div>
                    </div>
                    
                    <!-- 上传按钮 -->
                    <div class="upload-action" style="text-align: center; padding: 20px; background: #fff; border-radius: 6px; border: 1px solid #ddd;">
                      <button type="button" class="layui-btn layui-btn-primary layui-btn-lg layui-btn-radius" id="uploadFilesBtn" disabled>
                        <i class="layui-icon layui-icon-upload"></i> 开始上传
                      </button>
                      <div style="color: #666; font-size: 13px; margin-top: 10px;">
                        请选择WNG文件和Audio文件，然后点击开始上传
                      </div>
                      <div id="uploadProgress" style="margin-top: 10px; display: none;">
                        <div class="layui-progress">
                          <div class="layui-progress-bar" lay-percent="0%"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 上传结果记录区域 -->
                <div class="result-section" style="background: #fff; border-radius: 8px; padding: 25px; border: 1px solid #e9ecef;">
                  <div style="font-size: 18px; font-weight: bold; margin-bottom: 20px; color: #333; border-bottom: 2px solid #1E9FFF; padding-bottom: 10px;">
                    <i class="layui-icon layui-icon-table" style="color: #1E9FFF;"></i> 上传结果记录
                  </div>
                  <div style="margin-top: 15px;">
                    <table id="table2" lay-filter="lay2" class="layui-table" style="table-layout:fixed;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;">
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>

<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/main.min.js"></script>

<script type="text/javascript" src="addtask/layui-v2.8.3/layui/layui.js"></script>
<script type="text/javascript" src="js/vonrinfo3.js?v=2"></script>
<script type="text/html" id="page0">
  	<div class="layui-inline">
		        <input class="layui-input" name="id" id="tag0" autocomplete="off" placeholder="请输入...">
		      </div>
		      <button type="button"  id="envsearch" class="layui-btn layui-btn-sm"  lay-event="search">搜索</button>
		    </div>
</script>

<script type="text/html" id="bardown1">
	<a  class="layui-btn layui-btn-xs layui-bg-blue" lay-event="start1" >在线播放</a>
</script>
<script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看详情</a>
</script>

<script type="text/html" id="logQueryBar">
    <a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="query">
        <i class="layui-icon layui-icon-search"></i> 查询
    </a>
</script>

<script>
	function tap1() {
		var index = layer.open({
			type: 1, // page 层类型，其他类型详见「基础属性」
			 content: "http://**************:9200/iWork2Agent/test.jsp",
			 area:["500px","100px"]
			});
}
</script>
</body>
</html>
