/**
 * 
 */

var devinfo =[];
$.ajax({
	type: "get",
	url:'http://127.0.0.1:8080/iWork2Agent/starttest3?devNo=',
   async:false,
//   data: {jobid:padata.taskid,kpitemplate:padata.tem},
    dataType:'json',
   success: function(data){
		devinfo = data;
//		console.log(data);
//		chartx = data[2].x;
//		charty1 = data[2].y1;
//		charty21 = data[2].y21;
//		charty22 = data[2].y22;
//		datatable2=data[0];
//		document.getElementById("card1").innerHTML ="共"+data[1].buildnumber+"次";
//		document.getElementById("card2").innerHTML ="共"+data[1].buildcase+"条";
//		document.getElementById("card3").innerHTML =data[1].successrate;
//		document.getElementById("card4").innerHTML ="成功："+data[1].newbuildcase.split("-")[0]+"  失败："+data[1].newbuildcase.split("-")[1];
//		aidatatable = data[3];
	}
});

 
var myjson;

layui.use(function(){
  var laypage=layui.laypage;
  var layer = layui.layer;
  var table = layui.table;
  // 渲染
  table.render({
	type:'post',
    elem: '#table0',
    url:devinfo, // 此处为静态模拟数据，实际使用时需换成真实接口
    even: false,
    toolbar: '#page0',
    id:'testReload0',
    request: {
          pageName: 'pageIndex',  // 页码的参数名称，默认：page
          limitName: 'pageSize'   // 每页数据量的参数名，默认：limit
	
	  },
	response: {
          statusName: 'code',     // 规定数据状态的字段名称，默认：code
          statusCode: 0,          // 规定成功的状态码，默认：0
          countName: 'count', // 规定数据总数的字段名称，默认：count
          msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
          dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
	  },
	  
//	  					devlist.getJSONObject(i).put("devNo", devNo); 
//						devlist.getJSONObject(i).put("assetNo", assetNo);
//						devlist.getJSONObject(i).put("mtNo", mtNo);
//						devlist.getJSONObject(i).put("ip", ip);
//						devlist.getJSONObject(i).put("datetime", datetime.toString());
	  
    cols: [[
        {field:'devNo', title:'设备编号', width:'6%'},
        {field:'assetNo', title:'资产编号', width:'7%'},
        {field:'mtNo', title:'电话号码',width:'10%'},
        {field:'ip', title:'IP',width:'15%'},
        {field:'datetime', title:'更新时间',width:'15%'},
        {field:'filename', title:'文件名',width:'20%'},
        {field:'result', title:'结果',width:'10%'},
        {field:'lossInfo', title:'详情',width:'10%'},
        {field:'operate', title:'操作',width:'10%',toolbar:'#bardown1'},

      ]],
      done: function(res, curr, count){
	     var that = $("#table0").siblings();

     res.data.forEach(function (item, index) {
		console.log(item);
         if (item.rowId=="total") {
            var tr = that.find(".layui-table-box tbody tr[data-index='" + index + "']");
            tr.css("background-color", "#F8F8FF");
         }
//         else{
//			var tr = that.find(".layui-table-box tbody tr[data-index='" + index + "']");
//            tr.css("background-color", "");
//			}
    });
},
    
   page: {layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
	    curr: 1,      // 设置默认起始页1
	    groups: 5,   //只显示10个连续页码,就是说显示10个可见页其他的省略
	    first: false, // 不显示首页
	    last: false   // 不显示尾页
		},
   text:{
	    none:'暂无相关数据'},
	     limit: 20,
		limits: [5,10,15,20,100,1000]
  });


  
  		table.on('toolbar(lay0)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            switch(obj.event){
                case 'search': 
                    search();
                    break;
            }
        });

      table.on( 'tool(lay0)',function (obj) {
			console.log(obj);
			switch(obj.event){
			        case 'start1':
			        	start1(obj.data.dataid,obj.data.rowId);
		            break;
		
		    }
		}); 

 function search(){
				 	var demoReload = document.getElementById("tag0").value;
//	                console.log(demoReload);
	                table.reload('testReload0', {
                    url:"QueryVonrTableSearchServlet",
                    request: {
                              pageName: 'pageIndex',  // 页码的参数名称，默认：page
                              limitName: 'pageSize', // 每页数据量的参数名，默认：limit
//								search_data:'id'
                      },
                    response: {
                             statusName: 'code',     // 规定数据状态的字段名称，默认：code
                             statusCode: 0,          // 规定成功的状态码，默认：0
                             countName: 'count', // 规定数据总数的字段名称，默认：count
                             msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                             dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
                      },
                    page: {
                            layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                            curr: 1,      // 设置默认起始页1
                            groups: 10,   //只显示10个连续页码,就是说显示10个可见页其他的省略
                            first: false, // 不显示首页
                            last: false   // 不显示尾页
                        },
                        limit: 20,
                        limits: [5,10,15,20,100,1000]

                    ,where: {
                        search_data: encodeURI(demoReload),
                    }
                  }, 'data');
                  
             document.getElementById("tag0").value=demoReload;
             }
             
             
      function start1(dataid,rowid){
			var url = "https://iask.zte.com.cn/api/whisper/audio/"+dataid+"/"+rowid+"";
//			window.open(url);
		    layer.open({
		      type: 2,
		      title:false,
		      area: ['30%', '30%'],	
		      
		      content: url
		//      success: function(layero, index){
		//            layer.full(index);
		//          }
		    });
		}

});
 
 
                      
         
                                                     