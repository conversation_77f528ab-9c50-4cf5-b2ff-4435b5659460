var xmlHttp=false;  
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}
function init(){
	
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = initcallBack;
    	var url="ParambaseinfoServlet";  
    	xmlHttp.open("GET", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);	
}
function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			table(data[0])
		}
	}	
}

function table(data){
	var tbody1  = document.getElementById("tbody") ; 
	s="";
	for(var i=0;i<data.length;i++){
		if(i%2==0){
			s+="<tr bgcolor='#ECF5FF'>"
		}else{
			s+="<tr bgcolor='#FBFBFF'>"
		}
		s+="<td style='text-align:center'>"+(i+1)+"</td>";
		s+="<td style='text-align:center'>"+data[i].gnbid+"</td>";
		s+="<td style='text-align:center'>"+data[i].subnetwork+"</td>";
		s+="<td style='text-align:center'>"+data[i].pci+"</td>";
		s+="<td style='text-align:center'>"+data[i].celllocalid+"</td>";
		s+="<td style='text-align:center'>"+data[i].ssbFrequency+"</td>";
		s+="<td style='text-align:center'>"+data[i].version+"</td>";
		s+="<td style='text-align:center'>"+data[i].umeip+"</td>";
		s+="<td style='text-align:center'>"+"<input id='"+(i+1)+"' value='"+(i+1)+"' type='checkbox'>"+"</td>"
		s+="</tr>"
	}
	tbody1.innerHTML=s;
}

function queryinfo(){
	var t = document.getElementById( "tbody" );
	var rows = t.rows;
	var gnbids="";
	var pcis="";
	for(var i=0;i<rows.length;i++){
		ischeck = document.getElementById((i+1)).checked;	
		if(ischeck==false) continue;
		for(var j=0;j<rows[i].cells.length;j++){
			if(j==1){
					gnbids = gnbids+rows[i].cells[j].innerText+"#";
			}else if(j==3){
					pcis = pcis+rows[i].cells[j].innerText+"#";
				
			}
		}
	}
	gnbids = gnbids.substring(0,gnbids.length-1);
	pcis = pcis.substring(0,pcis.length-1);
    gnbids=encodeURIComponent(gnbids);
    pcis=encodeURIComponent(pcis);
    window.open ("paraminfo.jsp?gnbids="+gnbids+"&pcis="+pcis, "newwindow2", "height=500, width=1200, top=100, left=150,toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no")

//	createXMLHttpRequest();
//	xmlHttp.onreadystatechange = querycallBack;
//	var url=encodeURI("ParamqueryinfoServlet?gnbid="+gnbids+"&pci="+pcis);
//	xmlHttp.open("POST", url, true);
//	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
//	xmlHttp.send(null);	
}

//function querycallBack()
//{
//	if (xmlHttp.readyState == 4) {
//		if (xmlHttp.status == 200) {
//			var data=xmlHttp.responseText;
//			data = JSON.parse(data);
//			alert("sss");
//		}
//	}	
//}

//function tapcounter(){
//	if(ind==1 || ind ==2){
//		var td = event.srcElement;
//		tr =td.parentElement; 
//		var index = tr.rowIndex-1;
//		window.open ("kpicounter.jsp?index="+index+"&dev="+dev, "newwindow2", "height=500, width=1200, top=100, left=150,toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no")
//	}else{
//		
//	}
//}