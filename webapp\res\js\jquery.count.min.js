/*!
 count.js 1.0.0
 ©2020-2020 L.Z.C. <EMAIL>
*/
(function(a) {
    a.fn.numberRockFloat=function(options){
        var defaults={
            initNumber:0,
            lastNumber:0,
            fixedSize: 2,
            duration:2000,
            step: 0,
            easing:'swing'
        };
        var opts=$.extend({}, defaults, options);
        var temp = opts.step;
        var tempVal = 0;
        if(opts.initNumber > opts.lastNumber){
            tempVal = opts.initNumber- opts.lastNumber;
        }else{
            tempVal = opts.lastNumber- opts.initNumber;
        }

        a(this).html(opts.initNumber);

        a(this).animate({
            num : "numberRock"
        },{
            duration : opts.duration,
            easing : opts.easing,
            complete : function(){
            },
            step : function(a,b){
                if(opts.initNumber > opts.lastNumber){
                    if(--temp < 0){
                        temp = opts.step;
                        $(this).html(parseFloat(opts.initNumber - b.pos * tempVal).toFixed(opts.fixedSize));
                    }
                }else if(opts.initNumber < opts.lastNumber){
                    if(--temp < 0){
                        temp = opts.step;
                        $(this).html(parseFloat(b.pos * tempVal + opts.initNumber).toFixed(opts.fixedSize));
                    }
                }
            }
        });
    }

    a.fn.numberRockInt=function(options){
        var defaults={
            initNumber:0,
            lastNumber:0,
            duration:2000,
            step: 0,
            easing:'swing'
        };
        var opts=a.extend({}, defaults, options);
        var temp = opts.step;
        var tempVal = 0;
        if(opts.initNumber > opts.lastNumber){
            tempVal = opts.initNumber- opts.lastNumber;
        }else{
            tempVal = opts.lastNumber- opts.initNumber;
        }

        a(this).html(opts.initNumber);

        a(this).animate({
            num : "numberRock"
        },{
            duration : opts.duration,
            easing : opts.easing,
            complete : function(){
                a(this).html(opts.lastNumber);
            },
            step : function(a, b){
                if(opts.initNumber > opts.lastNumber){
                    if(--temp < 0){
                        temp = opts.step;
                        $(this).html(opts.initNumber - parseInt(b.pos * tempVal));
                    }
                }else if(opts.initNumber < opts.lastNumber){
                    if(--temp < 0){
                        temp = opts.step;
                        $(this).html(parseInt(b.pos * tempVal + opts.initNumber));
                    }
                }
            }
        });
    }
})(jQuery);
