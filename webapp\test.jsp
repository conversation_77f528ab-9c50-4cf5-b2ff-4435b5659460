<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>iWork自动化测试</title>
<meta name="author" content="xuejie10170365">
<link href="css/bootstrap.min.css" rel="stylesheet">
<link href="css/materialdesignicons.min.css" rel="stylesheet">
<link href="css/style.min.css" rel="stylesheet">
<link href="addtask/layui-v2.8.3/layui/css/layui.css" type="text/css" rel="stylesheet">
<link href="css/layer/layer.css" type="text/css" rel="stylesheet">

</head>
  
<body>
<div class="container-fluid p-t-15">
  
  <div class="row">
    <div class="col-md-12">
      <div class="card">
      <center style="padding-top: 20px">
              <a style="font-size: 30px;font-weight: 500;">测试工具箱<span class="layui-badge layui-bg-blue">iWork</span></a>        <div class="card-body">
      
      </center>
          <div class="tab-content">
            <div class="tab-pane fade active in" id="home-1ci">
				<table id="table0" lay-filter="lay0" class="layui-table" style="table-layout:fixed;overflow: hidden;white-space: nowrap;WORD-BREAK: break-all;">
				</table>
				
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
</div>

<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/main.min.js"></script>

<script type="text/javascript" src="addtask/layui-v2.8.3/layui/layui.js"></script>
<!-- <script type="text/javascript" src="js/usecase.js"></script> -->
<script type="text/javascript" src="js/testinfo.js"></script>
<script type="text/html" id="page0">
  	<div class="layui-inline">
		        <input class="layui-input" name="id" id="tag0" autocomplete="off" placeholder="请输入...">
		      </div>
		      <button type="button"  id="envsearch" class="layui-btn layui-btn-sm"  lay-event="search">搜索</button>
		    </div>
</script>
<script type="text/html" id="bardown1">
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="start1" >在线播放</a>
</script>
</body>
</html>