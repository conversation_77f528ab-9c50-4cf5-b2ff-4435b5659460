/**
 *
 */
var myjson;
var subTableId; // 定义全局变量
var logininfo = islogininfo(); // 获取登录信息

layui.use(function () {
    var laypage = layui.laypage;
    var layer = layui.layer;
    var table = layui.table;
    var upload = layui.upload;
    var element = layui.element;

    // 渲染主表格
    function renderMainTable() {
        table.render({
            type: 'post',
            elem: '#table0',
            url: 'QueryVonrTableServlet2', // 此处为静态模拟数据，实际使用时需换成真实接口
            even: false,
            toolbar: '#page0',
            id: 'testReload0',
            request: {
                pageName: 'pageIndex',  // 页码的参数名称，默认：page
                limitName: 'pageSize'   // 每页数据量的参数名，默认：limit
            },
            response: {
                statusName: 'code',     // 规定数据状态的字段名称，默认：code
                statusCode: 0,          // 规定成功的状态码，默认：0
                countName: 'count', // 规定数据总数的字段名称，默认：count
                msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
            },
            cols: [[
                { field: 'dataid', title: '任务ID', width: 80 },
                { field: 'rowId', title: '子任务ID', width: 80 },
                { field: 'status', title: '状态', width: 120, templet: function (d) {
                    if (d.status === "success") {
                        return '<span style="color: green;">' + d.status + '</span>';
                    } else if (d.status === "fail") {
                        return '<span style="color: red;">' + d.status + '</span>';
                    }
                } },
                { field: 'start', title: '开始时间', width: 200 },
                { field: 'end', title: '结束时间', width: 200 },
                { field: 'filename', title: '文件名', width: 300 },
                { field: 'result', title: '结果', width: 150 },
                { field: 'lossInfo', title: '详情', width: 250 },
                { field: 'operate', title: '操作', width: 150, toolbar: '#barDemo' },
            ]],
            done: function (res, curr, count) {
                bindMainTableToolEvent();
                	     var that = $("#table0").siblings();

//                	console.log(res);
                     res.data.forEach(function (item, index) {
			         if (item.rowId=="total") {
			            var tr = that.find(".layui-table-box tbody tr[data-index='" + index + "']");
			            tr.css("background-color", "#F1FAFA");
			         }
			    });
            },
            page: {
                layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                curr: 1,      // 设置默认起始页1
                groups: 5,   //只显示10个连续页码,就是说显示10个可见页其他的省略
                first: false, // 不显示首页
                last: false   // 不显示尾页
            },
            text: {
                none: '暂无相关数据'
            },
            limit: 20,
            limits: [5, 10, 15, 20, 100, 1000]
        });
    }

    // 渲染日志表格
    function renderLogTable() {
		var logininfo = islogininfo();
		var number = logininfo.split("#")[0];
		var people = logininfo.split("#")[1];
		var token = logininfo.split("#")[2];
		if(number ==""){
			return;
		}
        table.render({
            type: 'post',
            elem: '#table1',
            url: 'QueryVonrLogTableServlet?number='+number, // 查询日志表的接口
            even: false,
            toolbar: '#page1',
            id: 'testReload1',
            request: {
                pageName: 'pageIndex',  // 页码的参数名称，默认：page
                limitName: 'pageSize'   // 每页数据量的参数名，默认：limit
            },
            response: {
                statusName: 'code',     // 规定数据状态的字段名称，默认：code
                statusCode: 0,          // 规定成功的状态码，默认：0
                countName: 'count', // 规定数据总数的字段名称，默认：count
                msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
            },
            cols: [[
                { type: 'checkbox', fixed: 'left' },
                { field: 'dataid', title: '数据ID', width: 100 },
//                { field: 'userid', title: '用户ID', width: 120 },
                { field: 'filename', title: '文件名', width: 300 },
                { field: 'uploadtime', title: '上传时间', width: 200 },
                { field: 'path', title: '文件路径', width: 350 },
//                { field: 'deleted', title: '删除状态', width: 120, templet: function (d) {
//                    if (d.deleted === "true" || d.deleted === true) {
//                        return '<span style="color: red;">已删除</span>';
//                    } else {
//                        return '<span style="color: green;">正常</span>';
//                    }
//                } }
            ]],
            page: {
                layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                curr: 1,      // 设置默认起始页1
                groups: 5,   //只显示5个连续页码
                first: false, // 不显示首页
                last: false   // 不显示尾页
            },
            text: {
                none: '暂无相关数据'
            },
            limit: 20,
            limits: [5, 10, 15, 20, 100, 1000]
        });
    }

    // 渲染aivonrlog2表格
    function renderLog2Table() {
        table.render({
            type: 'post',
            elem: '#table2',
            url: 'QueryVonrLog2TableServlet', // 查询aivonrlog2表的接口
            even: false,
            id: 'testReload2',
            request: {
                pageName: 'pageIndex',  // 页码的参数名称，默认：page
                limitName: 'pageSize'   // 每页数据量的参数名，默认：limit
            },
            response: {
                statusName: 'code',     // 规定数据状态的字段名称，默认：code
                statusCode: 0,          // 规定成功的状态码，默认：0
                countName: 'count', // 规定数据总数的字段名称，默认：count
                msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
            },
            cols: [[
                { title: 'ID', width: 80,type:"numbers" },
                { field: 'dataid', title: '数据ID', width: 100 },
                { field: 'userid', title: '用户ID', width: 120 },
//                { field: 'filename', title: '文件名', width: 250 },
                { field: 'uploadtime', title: '上传时间', width: 180 },
                { field: 'status', title: '状态', width: 180 },
//                { field: 'status', title: '状态', width: 100, templet: function (d) {
//                    if (d.status === "success") {
//                        return '<span style="color: green;">成功</span>';
//                    } else if (d.status === "fail") {
//                        return '<span style="color: red;">失败</span>';
//                    } else {
//                        return '<span style="color: orange;">处理中</span>';
//                    }
//                } },
                { field: 'taskid', title: '任务ID', width: 200 },
                { field: 'query', title: '查询操作', width: 120, toolbar: '#logQueryBar' }
            ]],
            page: {
                layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                curr: 1,      // 设置默认起始页1
                groups: 5,   //只显示5个连续页码
                first: false, // 不显示首页
                last: false   // 不显示尾页
            },
            text: {
                none: '暂无相关数据'
            },
            limit: 20,
            limits: [5, 10, 15, 20, 100, 1000]
        });
    }

    // 绑定主表格工具条事件
    function bindMainTableToolEvent() {
        table.on('tool(lay0)', function (obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                var tr = obj.tr;
                var nextTr = tr.next();
                if (nextTr.hasClass('collapsible-content')) {
                    // 如果已经展开，就折叠当前行的子表格
                    nextTr.remove();
                } else {
                    // 如果未展开，就展开当前行的子表格
                    var subTableId = `subTable-${data.dataid}`;
                    var content = `<tr class="collapsible-content collapsible-row"><td colspan="9" style='padding-bottom: 10px; '><table style="margin-bottom: 5px;margin-top: 5px;width:90%" id="${subTableId}" lay-filter="${subTableId}-filter"></table></td></tr>`;
                    tr.after(content);

                    // 渲染子表格
                    table.render({
                        elem: `#${subTableId}`,
                        cols: [[
                            { field: 'dataid', title: '任务ID', width: 80 },
                            { field: 'rowId', title: '子任务ID', width: 80 },
                            { field: 'status', title: '状态', width: 120, templet: function (d) {
                                if (d.status === "success") {
                                    return '<span style="color: green;">' + d.status + '</span>';
                                } else if (d.status === "fail") {
                                    return '<span style="color: red;">' + d.status + '</span>';
                                }
                            } },
                            { field: 'start', title: '开始时间', width: 200 },
                            { field: 'end', title: '结束时间', width: 200 },
                            { field: 'filename', title: '文件名', width: 300 },
                            { field: 'result', title: '结果', width: 150 },
                            { field: 'lossInfo', title: '详情', width: 250 },
                            { field: 'operate', title: '操作', width: 150, toolbar: '#bardown1' },
                        ]],
                        data: data.subData,
                        done: function() {
                            // 确保子表格渲染完成后显示
                            $(`#${subTableId}`).closest('.collapsible-content').show();
                        }
                    });

                    // 监听子表格工具条
                    table.on(`tool(${subTableId}-filter)`, function (obj) {
                        console.log(obj);
                        switch (obj.event) {
                            case 'start1':
                                start1(obj.data.dataid, obj.data.rowId);
                                break;
                        }
                    });
                }
            }
        });
    }

    // 搜索功能
    function search() {
        var demoReload = document.getElementById("tag0").value;
        table.reload('testReload0', {
            url: "QueryVonrTableSearchServlet2",
            request: {
                pageName: 'pageIndex',  // 页码的参数名称，默认：page
                limitName: 'pageSize', // 每页数据量的参数名，默认：limit
            },
            response: {
                statusName: 'code',     // 规定数据状态的字段名称，默认：code
                statusCode: 0,          // 规定成功的状态码，默认：0
                countName: 'count', // 规定数据总数的字段名称，默认：count
                msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
            },
            page: {
                layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                curr: 1,      // 设置默认起始页1
                groups: 10,   //只显示10个连续页码,就是说显示10个可见页其他的省略
                first: false, // 不显示首页
                last: false   // 不显示尾页
            },
            limit: 20,
            limits: [5, 10, 15, 20, 100, 1000],
            where: {
                search_data: encodeURI(demoReload),
            }
        }, 'data');
        document.getElementById("tag0").value = demoReload;
    }

    // 日志搜索功能
    function searchLog() {
		var number = logininfo.split("#")[0];
		var people = logininfo.split("#")[1];
		var token = logininfo.split("#")[2];
		if(number ==""){
			return;
		}
        var demoReload = document.getElementById("tag1").value;
        table.reload('testReload1', {
            url: "QueryVonrLogTableSearchServlet?number="+number,
            request: {
                pageName: 'pageIndex',  // 页码的参数名称，默认：page
                limitName: 'pageSize', // 每页数据量的参数名，默认：limit
            },
            response: {
                statusName: 'code',     // 规定数据状态的字段名称，默认：code
                statusCode: 0,          // 规定成功的状态码，默认：0
                countName: 'count', // 规定数据总数的字段名称，默认：count
                msgName: 'msg',         // 规定状态信息的字段名称，默认：msg
                dataName: 'data' ,       // 规定数据列表的字段名称，默认：data
            },
            page: {
                layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],     // 自定义分页布局
                curr: 1,      // 设置默认起始页1
                groups: 10,   //只显示10个连续页码,就是说显示10个可见页其他的省略
                first: false, // 不显示首页
                last: false   // 不显示尾页
            },
            limit: 20,
            limits: [5, 10, 15, 20, 100, 1000],
            where: {
                search_data: encodeURI(demoReload),
            }
        }, 'data');
        document.getElementById("tag1").value = demoReload;
    }

    // 开始操作
    function start1(dataid, rowid) {
        var url = "https://iask.zte.com.cn/api/whisper/audio/" + dataid + "/" + rowid + "";
        layer.open({
            type: 2,
            title: false,
            area: ['30%', '30%'],
            content: url
        });
    }

    // 上传文件操作
    function uploadFile(data) {
        console.log("上传文件:", data);
       	var logininfo = islogininfo();
		var number = logininfo.split("#")[0];
		var people = logininfo.split("#")[1];
		var token = logininfo.split("#")[2];
		if(number ==""){
			return;
		}
		 $.ajax({
            url: 'ReportUploadServlet',
            type: 'POST',
            data: {
                dataid: data.dataid,
                number: number,
                token: token,

            },
            dataType: 'json',
            success: function(res) {
                layer.closeAll('loading');
                if (res.code === 0) {
					layer.msg('查询失败：' + (res.msg || '未知错误'), {icon: 2});

                } else {
                    layer.msg('查询失败：' + (res.msg || '未知错误'), {icon: 2});
                }
            },
            error: function() {
                layer.closeAll('loading');
                layer.msg('查询请求异常', {icon: 2});
            }
        });
    }

    // 查询并跳转操作
    function queryAndJump(data) {
        console.log("查询并跳转:", data);

        // 显示加载层
        layer.load();

        // 调用查询接口
        $.ajax({
            url: 'QueryVonrDetailServlet',
            type: 'GET',
            data: {
                dataid: data.taskid,
            },
            dataType: 'json',
            success: function(res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    // 跳转到详情页面，传递查询结果
                    var reporturl =  res.result.report_url;
                    
                    if(reporturl==""){
	                    window.open(detailUrl, '_blank');
	                    table.reload('testReload2');
					}else{
						alert("暂无分析报告生成");
						table.reload('testReload2');
					}
                } else {
                    layer.msg('查询失败：' + (res.msg || '未知错误'), {icon: 2});
                }
            },
            error: function() {
                layer.closeAll('loading');
                layer.msg('查询请求异常', {icon: 2});
            }
        });
    }

    // 批量上传功能
    function batchUpload(checkStatus) {
        var data = checkStatus.data;
        if (data.length === 0) {
            layer.msg('请先选择要上传的记录', {icon: 2});
            return;
        }

        var logininfo = islogininfo();
        var number = logininfo.split("#")[0];
        var people = logininfo.split("#")[1];
        var token = logininfo.split("#")[2];

        if (number === "") {
            layer.msg('获取用户信息失败', {icon: 2});
            return;
        }

        // 提取选中的dataid
        var dataids = [];
        for (var i = 0; i < data.length; i++) {
            dataids.push(data[i].dataid);
        }

        console.log('批量上传数据:', {
            dataids: dataids,
            number: number,
            token: token
        });

        // 显示确认对话框
        layer.confirm('确定要上传选中的 ' + dataids.length + ' 条LOG吗？', {
            btn: ['确定', '取消']
        }, function(index) {
            layer.close(index);

            // 显示加载层
            layer.load();

            // 发送批量上传请求
            $.ajax({
                url: 'ReportUploadServlet',
                type: 'POST',
                data: {
                    dataids: dataids.join(','),
                    number: number,
                    token: token
                },
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        // 刷新两个表格
                        table.reload('testReload1');
                        if (window.log2TableRendered) {
                            table.reload('testReload2');
                        } else {
                            renderLog2Table();
                            window.log2TableRendered = true;
                        }
                    } else {
                        layer.msg('上传失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('上传请求异常', {icon: 2});
                }
            });
        });
    }

    // 监听主表格工具栏事件
    table.on('toolbar(lay0)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event) {
            case 'search':
                search();
                break;
        }
    });

    // 监听日志表格工具栏事件
    table.on('toolbar(lay1)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event) {
            case 'search':
                searchLog();
                break;
            case 'batchUpload':
                batchUpload(checkStatus);
                break;
        }
    });

    // 监听日志表格行工具条事件
    table.on('tool(lay1)', function (obj) {
        var data = obj.data;
        console.log(data);
        switch (obj.event) {
            case 'upload':
                uploadFile(data);
                break;
        }
    });

    // 监听aivonrlog2表格行工具条事件
    table.on('tool(lay2)', function (obj) {
        var data = obj.data;
        console.log(data);
        switch (obj.event) {
            case 'query':
                queryAndJump(data);
                break;
        }
    });

    // 监听tab切换事件
    element.on('tab(mainTab)', function(data){
        console.log('切换到第' + (data.index + 1) + '个tab');
        if(data.index === 1) {
            // 切换到日志tab时，如果表格还没有渲染，则渲染它
            if(!window.logTableRendered) {
                setTimeout(function() {
                    renderLogTable();
                    window.logTableRendered = true;
                }, 100);
            }
            // 渲染log2表格（延迟渲染，避免影响性能）
            if(!window.log2TableRendered) {
                setTimeout(function() {
                    renderLog2Table();
                    window.log2TableRendered = true;
                }, 200);
            }
        }
    });

    // 初始化文件上传
    function initUpload() {
        var uploadInst = upload.render({
            elem: '#uploadMp3Btn',
            url: 'Mp3UploadServlet', // 上传接口
            accept: 'audio', // 只允许上传音频文件
            acceptMime: 'audio/mp3,audio/mpeg', // 只允许上传MP3格式
            exts: 'mp3', // 明确指定只允许mp3扩展名
            size: 50 * 1024, // 限制文件大小为50MB
            auto: true, // 选择文件后自动上传
            choose: function(obj) {
//                console.log("选择了文件:", obj);
            },
            before: function(obj) {
                // 上传前的回调
//                console.log("开始上传文件", obj);
                layer.load(); // 显示加载层
            },
            done: function(res) {
                // 上传完毕回调
//                console.log("上传响应:", res);
                layer.closeAll('loading'); // 关闭加载层
                if(res.code === 0) {
                    layer.msg(res.msg, {icon: 1,time:2000});
                    // 刷新表格数据
                    table.reload('testReload0');
                } else {
                    layer.msg('上传失败', {icon: 2});
                }
            },
            error: function() {
                // 请求异常回调
                layer.closeAll('loading');
                layer.msg('上传请求异常', {icon: 2});
            }
        });
    }

    // 初始化主表格
    renderMainTable();

    // 标记日志表格还未渲染
    window.logTableRendered = false;
    window.log2TableRendered = false;

    // 延迟初始化文件上传，确保DOM元素已经渲染完成
    setTimeout(function() {
        // 检查按钮是否存在
        var btn = document.getElementById('uploadMp3Btn');
        if (btn) {
//            console.log("找到上传按钮，开始初始化上传组件");
            initUpload();

            // 添加额外的点击事件监听器用于调试
            btn.addEventListener('click', function() {
//                console.log("上传按钮被点击了");
            });
        } else {
//            console.error("未找到上传按钮元素 #uploadMp3Btn");
        }
    }, 500);
});


function islogininfo(){
	var qqq="",www="",name="",token="";
	var idss = document.cookie.split(";");
	for(var i=0;i< idss.length;i++){
		var xx = idss[i];
		qqq=xx.split("=")[0];
		qqq=qqq.trim();
		if(qqq=="info")
		{ 
			www=xx.split("=")[1].split("#")[0];
			name=xx.split("=")[1].split("#")[1];
			token=xx.split("=")[1].split("#")[2];

		}
	}
		www = "10306639";
		name="陶森";
		token="72fbd8924d91aa52f9383328f424b139";
	return www+"#"+name+"#"+token;
}