window.addEventListener('load', function () {
    var arrow_l = document.querySelector('.arrow-l');
    var arrow_r = document.querySelector('.arrow-r');
    var focus = document.querySelector('.focus');
    var focusWidth = focus.offsetWidth;

    focus.addEventListener('mouseenter', function () {
        arrow_l.style.display = 'block';
        arrow_r.style.display = 'block';
        clearInterval(timer);
        timer = null;
    })
    focus.addEventListener('mouseleave', function () {
        arrow_l.style.display = 'none';
        arrow_r.style.display = 'none';
        timer = setInterval(function () {
            arrow_r.click();
        }, 3000);
    })

    var ul = focus.querySelector('ul');
    var ol = focus.querySelector('.circle');
    for (var i = 0; i < 3; i++) {
        //创建一个小li
        var li = document.createElement('li');

        li.setAttribute('index', i)

        ol.appendChild(li);

        //生成小圆圈，直接绑定点击事件  排他思想
        li.addEventListener('click', function () {
            for (var i = 0; i < ol.children.length; i++) {
                ol.children[i].className = '';
            }
            this.className = 'current';
            var index = this.getAttribute('index');
            num = index;
            circle = index;
            animate(ul, -index * focusWidth)

        })
    }
    //把ol里面的第一个小li设置类名为current
    ol.children[0].className = 'current';

    //复制第一个，添加到ul最后
//    var first = ul.children[0].cloneNode(true);
//    ul.appendChild(first);


    //点击左侧按钮，滚动
    var num = 0;
    var circle = 0;
    var flag = true;//节流阀
    arrow_r.addEventListener('click', function () {
        if (flag) {
            flag = false;//关闭节流阀
            if (num == ul.children.length - 1) {
                ul.style.left = 0;
                num = 0;
            }
            num++
            animate(ul, -num * focusWidth, function () {
                flag = true;//打开节流阀
            })
            circle++

            if (circle == ol.children.length) {
                circle = 0;
            }

            for (var i = 0; i < ol.children.length; i++) {
                ol.children[i].className = '';
            }
            ol.children[circle].className = 'current';
        }
    });

    //点击右侧按钮，滚动
    arrow_l.addEventListener('click', function () {
        if (flag) {
            flag = false;
            if (num == 0) {
                num = ul.children.length - 1;
                ul.style.left = -num * focusWidth + 'px';
            }
            num--
            animate(ul, -num * focusWidth, function () {
                flag = true;
            })

            circle--

            if (circle < 0) {
                circle = ol.children.length - 1;
            }

            for (var i = 0; i < ol.children.length; i++) {
                ol.children[i].className = '';
            }
            ol.children[circle].className = 'current';
        }
    });

    //自动播放
    var timer = setInterval(function () {
        arrow_r.click();
    }, 5000);

})