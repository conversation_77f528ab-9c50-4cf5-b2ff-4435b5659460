li {
    list-style: none;
}

.focus {
    position: relative;
     height: 500px; 
    overflow: hidden;    
}

.focus ul {
    position: absolute;
    top:0;
    left: 0;
    width: 500%;
}
.focus ul li {
    float: left;
}


.arrow-r,
.arrow-l {
    display: none;
    position: absolute;
    top: 40%;
    margin-top: -15px;
    width: 20px;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.5);
    text-align: center;
    line-height: 30px;
    color: #00fbfe;
    text-decoration: none;
    z-index: 5;
}

.arrow-l {
    left: 0;
    border-top-right-radius: 15px;
    border-bottom-right-radius: 15px;
}

.arrow-r {
    right: 0;
    border-top-left-radius: 15px;
    border-bottom-left-radius: 15px;
}

.focus ol  {
    position: absolute;
    bottom: 65px;
    left: 50%;
    margin-left: -35px;
    /* width: 70px; */
    height: 13px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 7px;
}

.focus ol li {
    float: left;
    width: 8px;
    height: 8px;
    background-color: #fff;
    border-radius: 50%;
    margin: 3px;

}

.focus ol .current {
    background-color: #ff5000;
}