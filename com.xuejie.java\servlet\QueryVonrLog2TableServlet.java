package servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.client.HttpClient;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;

/**
 * Servlet implementation class QueryVonrLog2TableServlet
 */
@WebServlet("/QueryVonrLog2TableServlet")
public class QueryVonrLog2TableServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private HttpClient customHttpClient = null;

    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryVonrLog2TableServlet() {
        super();
    }

    /**
     * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        doPost(request, response);
    }

    /**
     * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("text/html");
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");

        DaoCMCC dao = new DaoCMCC();

        String page = request.getParameter("pageIndex");
        String limit = request.getParameter("pageSize");
        int limit2 = Integer.valueOf(limit);
        int limit1 = (Integer.valueOf(page) - 1) * limit2;

        JSONArray jsonArray0 = new JSONArray();
        int count = 0;

        try {
            // 鏌ヨaivonrlog2琛ㄦ暟鎹�
            String sql0 = "SELECT * FROM aivonrlog2 ORDER BY uploadtime DESC LIMIT " + limit1 + "," + limit2 + ";";
            ResultSet rs0 = dao.executeQuery(sql0);
            String sql1 = "SELECT count(*) FROM aivonrlog2;";
            ResultSet rs1 = dao.executeQuery(sql1);

            try {
                jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
                if (rs1.next()) {
                    count = rs1.getInt(1);
                }
            } catch (JSONException | SQLException e) {
                e.printStackTrace();
            }

            JSONObject object = new JSONObject();
            object.put("code", 0).put("msg", "");
            object.put("count", count);
            object.put("data", jsonArray0);

            dao.close();

            PrintWriter out = response.getWriter();
            out.print(object);
            out.flush();
            out.close();

        } catch (Exception e) {
            e.printStackTrace();
            
            JSONObject errorObject = new JSONObject();
            errorObject.put("code", 1);
            errorObject.put("msg", "鏌ヨ澶辫触: " + e.getMessage());
            errorObject.put("count", 0);
            errorObject.put("data", new JSONArray());

            PrintWriter out = response.getWriter();
            out.print(errorObject);
            out.flush();
            out.close();
        }
    }

    public static String getRadomFileName() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        String str = simpleDateFormat.format(date);

        Random random = new Random();
        int rannum = (int) (random.nextDouble() * (99999999 - 10000000 + 1)) + 10000000;

        return rannum + str;
    }
}
